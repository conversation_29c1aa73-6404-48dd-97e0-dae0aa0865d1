<?php
/**
 * Database Cleanup Module for Redco Optimizer
 *
 * Cleans up unnecessary database entries to optimize database performance.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Database_Cleanup {

    /**
     * Module settings
     */
    private $settings = array();

    /**
     * Constructor
     */
    public function __construct() {
        if (redco_is_module_enabled('database-cleanup')) {
            $this->init();
        }
    }

    /**
     * Initialize the module
     */
    private function init() {
        // Load settings
        $this->load_settings();

        // Initialize hooks
        $this->init_hooks();
    }

    /**
     * Load module settings
     */
    private function load_settings() {
        $this->settings = array(
            'auto_cleanup' => redco_get_module_option('database-cleanup', 'auto_cleanup', false),
            'cleanup_interval' => redco_get_module_option('database-cleanup', 'cleanup_interval', 'weekly'),
            'cleanup_revisions' => redco_get_module_option('database-cleanup', 'cleanup_revisions', true),
            'cleanup_auto_drafts' => redco_get_module_option('database-cleanup', 'cleanup_auto_drafts', true),
            'cleanup_trashed_posts' => redco_get_module_option('database-cleanup', 'cleanup_trashed_posts', true),
            'cleanup_spam_comments' => redco_get_module_option('database-cleanup', 'cleanup_spam_comments', true),
            'cleanup_trashed_comments' => redco_get_module_option('database-cleanup', 'cleanup_trashed_comments', true),
            'cleanup_expired_transients' => redco_get_module_option('database-cleanup', 'cleanup_expired_transients', true),
            'cleanup_orphaned_postmeta' => redco_get_module_option('database-cleanup', 'cleanup_orphaned_postmeta', true),
            'cleanup_orphaned_commentmeta' => redco_get_module_option('database-cleanup', 'cleanup_orphaned_commentmeta', true),
            'keep_revisions' => redco_get_module_option('database-cleanup', 'keep_revisions', 5)
        );
    }

    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // Schedule automatic cleanup if enabled
        if ($this->settings['auto_cleanup']) {
            add_action('redco_database_cleanup_cron', array($this, 'run_automatic_cleanup'));

            if (!wp_next_scheduled('redco_database_cleanup_cron')) {
                wp_schedule_event(time(), $this->settings['cleanup_interval'], 'redco_database_cleanup_cron');
            }
        }

        // AJAX handlers
        add_action('wp_ajax_redco_database_cleanup', array($this, 'ajax_run_cleanup'));
        add_action('wp_ajax_redco_get_cleanup_stats', array($this, 'ajax_get_stats'));
    }

    /**
     * Run automatic cleanup
     */
    public function run_automatic_cleanup() {
        $this->run_cleanup();
    }

    /**
     * Run database cleanup
     */
    public function run_cleanup($options = null) {
        global $wpdb;

        if ($options === null) {
            $options = $this->settings;
        }

        // Log cleanup start for debugging
        error_log('Redco Optimizer: Starting database cleanup with options: ' . print_r($options, true));

        $results = array(
            'revisions_deleted' => 0,
            'auto_drafts_deleted' => 0,
            'trashed_posts_deleted' => 0,
            'spam_comments_deleted' => 0,
            'trashed_comments_deleted' => 0,
            'expired_transients_deleted' => 0,
            'orphaned_postmeta_deleted' => 0,
            'orphaned_commentmeta_deleted' => 0
        );

        // Clean up post revisions
        if (isset($options['cleanup_revisions']) && $options['cleanup_revisions']) {
            $keep_revisions = isset($options['keep_revisions']) ? (int) $options['keep_revisions'] : 5;
            $results['revisions_deleted'] = $this->cleanup_post_revisions($keep_revisions);
            error_log('Redco Optimizer: Cleaned ' . $results['revisions_deleted'] . ' post revisions');
        }

        // Clean up auto drafts
        if (isset($options['cleanup_auto_drafts']) && $options['cleanup_auto_drafts']) {
            $results['auto_drafts_deleted'] = $this->cleanup_auto_drafts();
            error_log('Redco Optimizer: Cleaned ' . $results['auto_drafts_deleted'] . ' auto drafts');
        }

        // Clean up trashed posts
        if (isset($options['cleanup_trashed_posts']) && $options['cleanup_trashed_posts']) {
            $results['trashed_posts_deleted'] = $this->cleanup_trashed_posts();
            error_log('Redco Optimizer: Cleaned ' . $results['trashed_posts_deleted'] . ' trashed posts');
        }

        // Clean up spam comments
        if (isset($options['cleanup_spam_comments']) && $options['cleanup_spam_comments']) {
            $results['spam_comments_deleted'] = $this->cleanup_spam_comments();
            error_log('Redco Optimizer: Cleaned ' . $results['spam_comments_deleted'] . ' spam comments');
        }

        // Clean up trashed comments
        if (isset($options['cleanup_trashed_comments']) && $options['cleanup_trashed_comments']) {
            $results['trashed_comments_deleted'] = $this->cleanup_trashed_comments();
            error_log('Redco Optimizer: Cleaned ' . $results['trashed_comments_deleted'] . ' trashed comments');
        }

        // Clean up expired transients
        if (isset($options['cleanup_expired_transients']) && $options['cleanup_expired_transients']) {
            $results['expired_transients_deleted'] = $this->cleanup_expired_transients();
            error_log('Redco Optimizer: Cleaned ' . $results['expired_transients_deleted'] . ' expired transients');
        }

        // Clean up orphaned postmeta
        if (isset($options['cleanup_orphaned_postmeta']) && $options['cleanup_orphaned_postmeta']) {
            $results['orphaned_postmeta_deleted'] = $this->cleanup_orphaned_postmeta();
            error_log('Redco Optimizer: Cleaned ' . $results['orphaned_postmeta_deleted'] . ' orphaned postmeta');
        }

        // Clean up orphaned commentmeta
        if (isset($options['cleanup_orphaned_commentmeta']) && $options['cleanup_orphaned_commentmeta']) {
            $results['orphaned_commentmeta_deleted'] = $this->cleanup_orphaned_commentmeta();
            error_log('Redco Optimizer: Cleaned ' . $results['orphaned_commentmeta_deleted'] . ' orphaned commentmeta');
        }

        // Optimize database tables
        $this->optimize_database_tables();

        // Update statistics
        $this->update_cleanup_stats($results);

        // Log cleanup completion
        error_log('Redco Optimizer: Database cleanup completed with results: ' . print_r($results, true));

        return $results;
    }

    /**
     * Clean up post revisions
     */
    private function cleanup_post_revisions($keep_revisions = 5) {
        global $wpdb;

        if ($keep_revisions <= 0) {
            // Delete all revisions
            $deleted = $wpdb->query("DELETE FROM {$wpdb->posts} WHERE post_type = 'revision'");
        } else {
            // Use a more compatible approach for older MySQL versions
            // First, get all posts that have revisions
            $posts_with_revisions = $wpdb->get_col("
                SELECT DISTINCT post_parent
                FROM {$wpdb->posts}
                WHERE post_type = 'revision'
                AND post_parent > 0
            ");

            $deleted = 0;

            foreach ($posts_with_revisions as $post_id) {
                // Get revision IDs for this post, ordered by date (newest first)
                $revision_ids = $wpdb->get_col($wpdb->prepare("
                    SELECT ID
                    FROM {$wpdb->posts}
                    WHERE post_type = 'revision'
                    AND post_parent = %d
                    ORDER BY post_date DESC
                ", $post_id));

                // If we have more revisions than we want to keep, delete the excess
                if (count($revision_ids) > $keep_revisions) {
                    $revisions_to_delete = array_slice($revision_ids, $keep_revisions);
                    $ids_to_delete = implode(',', array_map('intval', $revisions_to_delete));

                    if (!empty($ids_to_delete)) {
                        $result = $wpdb->query("DELETE FROM {$wpdb->posts} WHERE ID IN ({$ids_to_delete})");
                        $deleted += $result ?: 0;
                    }
                }
            }
        }

        return $deleted;
    }

    /**
     * Clean up auto drafts
     */
    private function cleanup_auto_drafts() {
        global $wpdb;

        $deleted = $wpdb->query("
            DELETE FROM {$wpdb->posts}
            WHERE post_status = 'auto-draft'
            AND post_date < DATE_SUB(NOW(), INTERVAL 7 DAY)
        ");

        return $deleted ?: 0;
    }

    /**
     * Clean up trashed posts
     */
    private function cleanup_trashed_posts() {
        global $wpdb;

        $deleted = $wpdb->query("
            DELETE FROM {$wpdb->posts}
            WHERE post_status = 'trash'
            AND post_date < DATE_SUB(NOW(), INTERVAL 30 DAY)
        ");

        return $deleted ?: 0;
    }

    /**
     * Clean up spam comments
     */
    private function cleanup_spam_comments() {
        global $wpdb;

        $deleted = $wpdb->query("DELETE FROM {$wpdb->comments} WHERE comment_approved = 'spam'");

        return $deleted ?: 0;
    }

    /**
     * Clean up trashed comments
     */
    private function cleanup_trashed_comments() {
        global $wpdb;

        $deleted = $wpdb->query("DELETE FROM {$wpdb->comments} WHERE comment_approved = 'trash'");

        return $deleted ?: 0;
    }

    /**
     * Clean up expired transients
     */
    private function cleanup_expired_transients() {
        global $wpdb;

        // First, delete expired timeout entries
        $deleted = $wpdb->query("
            DELETE FROM {$wpdb->options}
            WHERE option_name LIKE '_transient_timeout_%'
            AND option_value < UNIX_TIMESTAMP()
        ");

        // Get all remaining timeout entries to find orphaned transients
        $timeout_names = $wpdb->get_col("
            SELECT option_name
            FROM {$wpdb->options}
            WHERE option_name LIKE '_transient_timeout_%'
        ");

        // Convert timeout names to transient names
        $valid_transient_names = array();
        foreach ($timeout_names as $timeout_name) {
            $valid_transient_names[] = str_replace('_transient_timeout_', '_transient_', $timeout_name);
        }

        // Delete orphaned transients (those without timeout entries)
        if (!empty($valid_transient_names)) {
            $placeholders = implode(',', array_fill(0, count($valid_transient_names), '%s'));
            $orphaned_deleted = $wpdb->query($wpdb->prepare("
                DELETE FROM {$wpdb->options}
                WHERE option_name LIKE '_transient_%'
                AND option_name NOT LIKE '_transient_timeout_%'
                AND option_name NOT IN ({$placeholders})
            ", $valid_transient_names));

            $deleted += $orphaned_deleted ?: 0;
        } else {
            // If no valid timeouts exist, delete all non-timeout transients
            $orphaned_deleted = $wpdb->query("
                DELETE FROM {$wpdb->options}
                WHERE option_name LIKE '_transient_%'
                AND option_name NOT LIKE '_transient_timeout_%'
            ");

            $deleted += $orphaned_deleted ?: 0;
        }

        return $deleted;
    }

    /**
     * Clean up orphaned postmeta
     */
    private function cleanup_orphaned_postmeta() {
        global $wpdb;

        $deleted = $wpdb->query("
            DELETE pm FROM {$wpdb->postmeta} pm
            LEFT JOIN {$wpdb->posts} p ON pm.post_id = p.ID
            WHERE p.ID IS NULL
        ");

        return $deleted ?: 0;
    }

    /**
     * Clean up orphaned commentmeta
     */
    private function cleanup_orphaned_commentmeta() {
        global $wpdb;

        $deleted = $wpdb->query("
            DELETE cm FROM {$wpdb->commentmeta} cm
            LEFT JOIN {$wpdb->comments} c ON cm.comment_id = c.comment_ID
            WHERE c.comment_ID IS NULL
        ");

        return $deleted ?: 0;
    }

    /**
     * Optimize database tables
     */
    private function optimize_database_tables() {
        global $wpdb;

        $tables = $wpdb->get_col("SHOW TABLES");

        foreach ($tables as $table) {
            $wpdb->query("OPTIMIZE TABLE {$table}");
        }
    }

    /**
     * Get cleanup statistics
     */
    public function get_cleanup_stats() {
        return redco_get_cleanup_stats();
    }

    /**
     * Update cleanup statistics
     */
    private function update_cleanup_stats($results) {
        $stats = get_option('redco_cleanup_history', array());

        $stats[] = array(
            'date' => current_time('mysql'),
            'results' => $results
        );

        // Keep only last 10 cleanup records
        $stats = array_slice($stats, -10);

        update_option('redco_cleanup_history', $stats);
    }

    /**
     * Process cleanup options from POST data
     */
    private function process_cleanup_options($post_data) {
        // If options are provided in POST data, use them
        if (isset($post_data['options']) && is_array($post_data['options'])) {
            $options = $post_data['options'];

            // Convert string values to appropriate types
            $processed_options = array();

            // Boolean options
            $boolean_options = array(
                'cleanup_revisions',
                'cleanup_auto_drafts',
                'cleanup_trashed_posts',
                'cleanup_spam_comments',
                'cleanup_trashed_comments',
                'cleanup_expired_transients',
                'cleanup_orphaned_postmeta',
                'cleanup_orphaned_commentmeta'
            );

            foreach ($boolean_options as $option) {
                $processed_options[$option] = isset($options[$option]) && $options[$option] == '1';
            }

            // Numeric options
            $processed_options['keep_revisions'] = isset($options['keep_revisions']) ? (int) $options['keep_revisions'] : 5;

            return $processed_options;
        }

        // Fallback to current settings
        return $this->settings;
    }

    /**
     * AJAX handler for running cleanup
     */
    public function ajax_run_cleanup() {
        check_ajax_referer('redco_optimizer_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'redco-optimizer'));
        }

        try {
            // Process options from POST data
            $options = $this->process_cleanup_options($_POST);

            // Run the cleanup with processed options
            $results = $this->run_cleanup($options);

            // Calculate total items cleaned
            $total_cleaned = array_sum($results);

            // Create a detailed message
            $message_parts = array();
            if ($results['revisions_deleted'] > 0) {
                $message_parts[] = sprintf(__('%d post revisions', 'redco-optimizer'), $results['revisions_deleted']);
            }
            if ($results['auto_drafts_deleted'] > 0) {
                $message_parts[] = sprintf(__('%d auto drafts', 'redco-optimizer'), $results['auto_drafts_deleted']);
            }
            if ($results['trashed_posts_deleted'] > 0) {
                $message_parts[] = sprintf(__('%d trashed posts', 'redco-optimizer'), $results['trashed_posts_deleted']);
            }
            if ($results['spam_comments_deleted'] > 0) {
                $message_parts[] = sprintf(__('%d spam comments', 'redco-optimizer'), $results['spam_comments_deleted']);
            }
            if ($results['trashed_comments_deleted'] > 0) {
                $message_parts[] = sprintf(__('%d trashed comments', 'redco-optimizer'), $results['trashed_comments_deleted']);
            }
            if ($results['expired_transients_deleted'] > 0) {
                $message_parts[] = sprintf(__('%d expired transients', 'redco-optimizer'), $results['expired_transients_deleted']);
            }
            if ($results['orphaned_postmeta_deleted'] > 0) {
                $message_parts[] = sprintf(__('%d orphaned post meta', 'redco-optimizer'), $results['orphaned_postmeta_deleted']);
            }
            if ($results['orphaned_commentmeta_deleted'] > 0) {
                $message_parts[] = sprintf(__('%d orphaned comment meta', 'redco-optimizer'), $results['orphaned_commentmeta_deleted']);
            }

            if ($total_cleaned > 0) {
                $message = sprintf(__('Database cleanup completed successfully! Cleaned: %s', 'redco-optimizer'), implode(', ', $message_parts));
            } else {
                $message = __('Database cleanup completed - no items needed cleaning', 'redco-optimizer');
            }

            wp_send_json_success(array(
                'message' => $message,
                'results' => $results,
                'total_cleaned' => $total_cleaned
            ));

        } catch (Exception $e) {
            error_log('Redco Optimizer: Database cleanup error: ' . $e->getMessage());
            wp_send_json_error(array(
                'message' => __('Database cleanup failed: ', 'redco-optimizer') . $e->getMessage()
            ));
        }
    }

    /**
     * AJAX handler for getting stats
     */
    public function ajax_get_stats() {
        check_ajax_referer('redco_optimizer_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'redco-optimizer'));
        }

        $stats = $this->get_cleanup_stats();

        wp_send_json_success(array(
            'stats' => $stats
        ));
    }
}

// Initialize the module
new Redco_Database_Cleanup();
