/**
 * Smart WebP Conversion Admin JavaScript
 *
 * @package Redco_Optimizer
 * @subpackage Smart_WebP_Conversion
 * @since 1.0.0
 */

// Immediate script loading verification
console.log('🔧 WebP Admin JavaScript file loaded');
window.webpScriptLoaded = true;

// Define global test functions immediately (before IIFE)
window.testWebPInit = function() {
    console.log('🔧 Manual initialization test called');
    if (typeof window.WebPAdmin !== 'undefined') {
        window.WebPAdmin.init();
    } else {
        console.error('❌ WebPAdmin not available');
    }
};

window.testWebPModal = function() {
    console.log('🔧 Manual modal test called');
    if (typeof window.WebPAdmin !== 'undefined') {
        if (!window.WebPAdmin.initialized) {
            console.log('🔧 Initializing WebP Admin first...');
            window.WebPAdmin.init();
        }
        window.WebPAdmin.showModal();
    } else {
        console.error('❌ WebPAdmin not available');
    }
};

window.testWebPDebug = function() {
    console.log('🔧 Manual debug test called');
    if (typeof window.WebPAdmin !== 'undefined') {
        if (!window.WebPAdmin.initialized) {
            window.WebPAdmin.init();
        }
        window.WebPAdmin.debugImageDetection();
    } else {
        console.error('❌ WebPAdmin not available');
    }
};

console.log('✅ Global WebP test functions defined immediately');

(function($) {
    'use strict';

    var WebPAdmin = {

        // Properties
        isConverting: false,
        conversionData: {
            totalImages: 0,
            processedImages: 0,
            errorCount: 0,
            totalSavings: 0,
            currentBatch: 0,
            batchSize: 10
        },

        // Initialize
        init: function() {
            if (this.initialized) {
                console.log('⚠️ WebP Admin already initialized, skipping...');
                return;
            }

            console.log('🔧 Starting WebP Admin initialization...');
            this.initProgressModal();
            this.bindEvents();
            this.updateStats();
            this.initialized = true;
            console.log('✅ WebP Admin initialization complete');
        },

        // Initialize progress modal (following working Diagnostic pattern)
        initProgressModal: function() {
            console.log('🔧 Initializing WebP progress modal...');

            // Create progress modal HTML if it doesn't exist
            if ($('#webp-progress-modal').length === 0) {
                console.log('🔧 Creating modal HTML...');
                const modalHtml = `
                    <div id="webp-progress-modal" class="redco-modal" style="display: none;">
                        <div class="redco-modal-content">
                            <div class="redco-modal-header">
                                <h3 id="webp-progress-modal-title">Converting Images to WebP</h3>
                            </div>
                            <div class="redco-modal-body">
                                <div class="progress-container">
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 0%"></div>
                                    </div>
                                    <div class="progress-stats">
                                        <span class="progress-current">0</span> / <span class="progress-total">0</span> images processed
                                    </div>
                                </div>
                                <div class="conversion-stats">
                                    <div class="stat-item">
                                        <span class="stat-label">Processed:</span>
                                        <span class="stat-value processed-count">0</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">Errors:</span>
                                        <span class="stat-value error-count">0</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">Total Savings:</span>
                                        <span class="stat-value total-savings">0 KB</span>
                                    </div>
                                </div>
                                <div class="current-operation">
                                    <div class="current-file">Preparing conversion...</div>
                                </div>
                                <div class="conversion-log">
                                    <div class="log-header">Conversion Log</div>
                                    <div class="log-entries"></div>
                                </div>
                            </div>
                            <div class="redco-modal-footer">
                                <button type="button" class="button button-secondary" id="cancel-conversion">Cancel</button>
                                <button type="button" class="button button-primary" id="close-conversion-modal" style="display: none;">Close</button>
                            </div>
                        </div>
                    </div>
                `;
                $('body').append(modalHtml);
                console.log('✅ Modal HTML appended to body');
            } else {
                console.log('✅ Modal already exists in DOM');
            }
        },

        // Initialize WebP Admin
        init: function() {
            if (this.initialized) {
                console.log('⚠️ WebP Admin already initialized');
                return;
            }

            console.log('🔧 Initializing WebP Admin...');
            console.log('🔧 Module enabled status:', redcoWebP.module_enabled);
            console.log('🔧 Available nonces:', redcoWebP.nonces);

            this.bindEvents();
            this.updateStats();
            this.initialized = true;
            console.log('✅ WebP Admin initialized successfully');
        },

        // Bind event handlers
        bindEvents: function() {
            // Use event delegation for better compatibility
            $(document).on('click', '#bulk-convert-images', function(e) {
                e.preventDefault();

                const $button = $(this);

                // Check if button is disabled
                if ($button.prop('disabled')) {
                    alert('WebP conversion is not available. Your server may not support WebP conversion. Please contact your hosting provider to enable GD library with WebP support.');
                    return;
                }

                console.log('🔧 Bulk convert button clicked, starting conversion...');
                WebPAdmin.startBulkConversion();
            });

            // Test modal button
            $(document).on('click', '#test-webp-modal', function(e) {
                e.preventDefault();
                console.log('🔧 Test modal button clicked');
                WebPAdmin.showModal();
            });

            // Debug image detection button
            $(document).on('click', '#debug-image-detection', function(e) {
                e.preventDefault();
                console.log('🔧 Debug button clicked');
                WebPAdmin.debugImageDetection();
            });

            // Simple AJAX test button
            $(document).on('click', '#test-simple-ajax', function(e) {
                e.preventDefault();
                console.log('🔧 Simple AJAX test button clicked');
                WebPAdmin.testSimpleAjax();
            });

            // Test image detection button
            $(document).on('click', '#test-image-detection', function(e) {
                e.preventDefault();
                console.log('🔧 Test image detection button clicked');
                WebPAdmin.testImageDetection();
            });

            // Test server support button
            $('#test-webp-support').on('click', this.testServerSupport.bind(this));

            // Modal close buttons
            $('#close-conversion-modal, #cancel-conversion').on('click', this.closeModal.bind(this));

            // Quality slider - update display only, auto-save is handled globally
            $('#quality').on('input', function() {
                $('#quality-value').text($(this).val());
            });

            // Lossless toggle
            $('#lossless').on('change', function() {
                if ($(this).is(':checked')) {
                    $('#quality-setting').slideUp();
                } else {
                    $('#quality-setting').slideDown();
                }
            });

            // WebP checkbox change handlers - use event delegation to avoid conflicts with auto-save
            $(document).on('change', '.webp-checkbox', this.updateWebPSummary.bind(this));

            // Auto-refresh stats every 30 seconds
            setInterval(this.updateStats.bind(this), 30000);

            // Initialize summary
            this.updateWebPSummary();
        },

        // Start bulk conversion process
        startBulkConversion: function() {
            if (this.isConverting) {
                return;
            }

            // Reset conversion data
            this.conversionData = {
                totalImages: 0,
                processedImages: 0,
                errorCount: 0,
                totalSavings: 0,
                currentBatch: 0,
                batchSize: parseInt($('#batch_size').val()) || 10
            };

            // Show modal
            this.showModal();
            this.isConverting = true;

            // Start conversion
            this.processBatch();
        },

        // Process a batch of images
        processBatch: function() {
            var self = this;

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_bulk_convert',
                    nonce: redcoWebP.nonces.bulk_convert,
                    batch_size: self.conversionData.batchSize,
                    offset: self.conversionData.currentBatch * self.conversionData.batchSize,
                    total_processed: self.conversionData.processedImages
                },
                success: function(response) {
                    console.log('✅ Bulk convert response:', response);
                    if (response.success) {
                        self.handleBatchSuccess(response);
                    } else {
                        const errorMsg = response.data && response.data.message ? response.data.message : 'Batch conversion failed';
                        self.handleError(errorMsg);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ Bulk convert AJAX failed:', xhr.status, xhr.statusText, xhr.responseText);
                    let errorMsg = 'Network error during conversion';
                    if (xhr.status === 400) {
                        errorMsg = 'Bad request - check if WebP module is enabled';
                    } else if (xhr.status === 403) {
                        errorMsg = 'Permission denied - insufficient privileges';
                    } else if (xhr.status === 500) {
                        errorMsg = 'Server error during conversion';
                    }
                    self.handleError(errorMsg);
                }
            });
        },

        // Handle successful batch conversion
        handleBatchSuccess: function(response) {
            // Update conversion data
            this.conversionData.processedImages = response.total_processed;
            this.conversionData.errorCount += response.errors.length;

            // Calculate total savings
            var batchSavings = 0;
            response.conversions.forEach(function(conversion) {
                batchSavings += conversion.savings;
            });
            this.conversionData.totalSavings += batchSavings;

            // Update progress display
            this.updateProgress(response);

            // Log conversions
            this.logConversions(response.conversions, response.errors);

            // Continue with next batch if there are more images
            if (response.has_more && this.isConverting) {
                this.conversionData.currentBatch++;
                setTimeout(this.processBatch.bind(this), 1000); // Small delay between batches
            } else {
                this.completeConversion();
            }
        },

        // Update progress display
        updateProgress: function(response) {
            var processed = this.conversionData.processedImages;
            var total = processed + (response.has_more ? this.conversionData.batchSize : 0);

            // Update progress bar
            var percentage = total > 0 ? Math.round((processed / total) * 100) : 0;
            $('.progress-fill').css('width', percentage + '%');

            // Update progress text
            $('.progress-current').text(processed);
            $('.progress-total').text(total);

            // Update stats
            $('.processed-count').text(processed);
            $('.error-count').text(this.conversionData.errorCount);
            $('.total-savings').text(this.formatFileSize(this.conversionData.totalSavings));
        },

        // Log conversion results
        logConversions: function(conversions, errors) {
            var logContainer = $('.log-entries');

            // Log successful conversions
            conversions.forEach(function(conversion) {
                var logEntry = $('<div class="log-entry log-success">')
                    .html('<span class="dashicons dashicons-yes"></span> ' +
                          conversion.title + ' - Saved ' +
                          this.formatFileSize(conversion.savings));
                logContainer.append(logEntry);
            }.bind(this));

            // Log errors
            errors.forEach(function(error) {
                var logEntry = $('<div class="log-entry log-error">')
                    .html('<span class="dashicons dashicons-no"></span> ' +
                          error.title + ' - ' + error.error);
                logContainer.append(logEntry);
            });

            // Scroll to bottom
            logContainer.scrollTop(logContainer[0].scrollHeight);
        },

        // Complete conversion process
        completeConversion: function() {
            this.isConverting = false;

            // Update modal
            $('.modal-header h2').text(redcoWebP.strings.complete);
            $('#cancel-conversion').hide();
            $('#close-conversion-modal').show();

            // Update stats
            this.updateStats();

            // Show completion message
            var message = 'Conversion completed! Processed ' +
                         this.conversionData.processedImages + ' images';

            if (this.conversionData.totalSavings > 0) {
                message += ', saved ' + this.formatFileSize(this.conversionData.totalSavings);
            }

            if (this.conversionData.errorCount > 0) {
                message += ' (' + this.conversionData.errorCount + ' errors)';
            }

            $('.current-file').html('<strong>' + message + '</strong>');
        },

        // Test server support
        testServerSupport: function() {
            var button = $('#test-webp-support');
            var originalText = button.text();

            button.prop('disabled', true).text('Testing...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_test_conversion',
                    nonce: redcoWebP.nonces.test
                },
                success: function(response) {
                    button.prop('disabled', false).text(originalText);

                    if (response.success) {
                        alert('✓ ' + response.message);
                    } else {
                        alert('✗ ' + response.message);
                    }

                    // Log capabilities to console
                    console.log('WebP Capabilities:', response.capabilities);
                },
                error: function() {
                    button.prop('disabled', false).text(originalText);
                    alert('Error testing server support');
                }
            });
        },

        // Update statistics
        updateStats: function() {
            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_get_stats',
                    nonce: redcoWebP.nonces.stats
                },
                success: function(response) {
                    console.log('✅ Stats updated:', response);
                    if (response.success !== false) {
                        // Update header metrics
                        $('.metric-card').eq(0).find('.metric-value').text(response.converted_images.toLocaleString());
                        $('.metric-card').eq(1).find('.metric-value').text(response.conversion_percentage + '%');
                        $('.metric-card').eq(2).find('.metric-value').text(WebPAdmin.formatFileSize(response.total_savings));

                        // Update sidebar stats
                        $('.stat-item').eq(0).find('.stat-value').text(response.total_images.toLocaleString());
                        $('.stat-item').eq(1).find('.stat-value').text(response.converted_images.toLocaleString());
                        $('.stat-item').eq(2).find('.stat-value').text(response.unconverted_images.toLocaleString());
                        $('.stat-item').eq(3).find('.stat-value').text(WebPAdmin.formatFileSize(response.total_savings));
                        $('.stat-item').eq(4).find('.stat-value').text(response.savings_percentage + '%');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ Stats update failed:', xhr.status, xhr.statusText);
                    // Don't show user errors for background updates
                }
            });
        },

        // Show conversion modal (following working Diagnostic pattern)
        showModal: function() {
            console.log('🔧 WebP showModal called');

            const $modal = $('#webp-progress-modal');
            console.log('🔧 Modal element found:', $modal.length);

            if ($modal.length === 0) {
                console.error('❌ Modal element not found in DOM');
                alert('Modal not found! Check console for details.');
                return;
            }

            console.log('🔧 Showing modal...');
            $('#webp-progress-modal-title').text('Converting Images to WebP');
            $modal.show();
            $('#close-conversion-modal').hide();

            // Reset modal content
            $('.progress-fill').css('width', '0%');
            $('.progress-current').text('0');
            $('.progress-total').text('0');
            $('.processed-count').text('0');
            $('.error-count').text('0');
            $('.total-savings').text('0 KB');
            $('.log-entries').empty();
            $('.current-file').text('Preparing conversion...');

            // Show/hide buttons
            $('#cancel-conversion').show();

            console.log('✅ Modal should now be visible');
        },

        // Debug image detection
        debugImageDetection: function() {
            console.log('🔧 Testing image detection...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_debug_images',
                    nonce: redcoWebP.nonce || redcoWebP.nonces.stats
                },
                success: function(response) {
                    console.log('✅ Debug response received:', response);
                    if (response.success) {
                        alert('Debug Results:\n' +
                              'Total Images: ' + response.data.total_images + '\n' +
                              'Image IDs: ' + response.data.image_ids.join(', ') + '\n' +
                              'MIME Types: ' + response.data.mime_types.join(', '));
                    } else {
                        alert('Debug failed: ' + response.data);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ AJAX request failed:', xhr.status, xhr.statusText);
                    alert('Debug request failed: ' + xhr.status + ' ' + xhr.statusText);
                }
            });
        },

        // Test simple AJAX (no nonce required)
        testSimpleAjax: function() {
            console.log('🔧 Testing simple AJAX...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_test_simple'
                },
                success: function(response) {
                    console.log('✅ Simple AJAX response:', response);
                    alert('Simple AJAX Test Result:\n' + response.data.message);
                },
                error: function(xhr, status, error) {
                    console.error('❌ Simple AJAX failed:', {
                        status: xhr.status,
                        statusText: xhr.statusText,
                        responseText: xhr.responseText,
                        error: error
                    });
                    alert('Simple AJAX failed: ' + xhr.status + ' ' + xhr.statusText);
                }
            });
        },

        // Test image detection query
        testImageDetection: function() {
            console.log('🔧 Testing image detection query...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_test_image_detection'
                },
                success: function(response) {
                    console.log('✅ Image detection test response:', response);
                    if (response.success) {
                        const data = response.data;
                        let message = 'Image Detection Test Results:\n\n';
                        message += 'Total Images: ' + data.all_images_count + '\n';
                        message += 'Unconverted Images: ' + data.unconverted_images_count + '\n';
                        message += 'Existing Conversions: ' + data.existing_conversions_count + '\n\n';

                        if (data.all_images_count > 0) {
                            message += 'All Images:\n';
                            data.all_images.forEach(function(img, index) {
                                message += (index + 1) + '. ID: ' + img.ID + ' - ' + img.post_mime_type + ' - ' + (img.post_title || 'No title') + '\n';
                            });
                        }

                        if (data.unconverted_images_count > 0) {
                            message += '\nUnconverted Images:\n';
                            data.unconverted_images.forEach(function(img, index) {
                                message += (index + 1) + '. ID: ' + img.ID + ' - ' + img.post_mime_type + ' - ' + (img.post_title || 'No title') + '\n';
                            });
                        }

                        alert(message);
                    } else {
                        alert('Image detection test failed: ' + response.data);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ Image detection test failed:', xhr.status, xhr.statusText);
                    alert('Image detection test failed: ' + xhr.status + ' ' + xhr.statusText);
                }
            });
        },

        // Close modal
        closeModal: function() {
            if (this.isConverting) {
                if (confirm('Are you sure you want to cancel the conversion?')) {
                    this.isConverting = false;
                    $('#webp-progress-modal').hide();
                }
            } else {
                $('#webp-progress-modal').hide();
            }
        },

        // Handle errors
        handleError: function(message) {
            this.isConverting = false;
            $('.current-file').html('<span style="color: #d63638;"><strong>Error: ' + message + '</strong></span>');
            $('#cancel-conversion').hide();
            $('#close-conversion-modal').show();
        },

        // Update WebP settings summary
        updateWebPSummary: function() {
            var enabledCount = 0;
            var estimatedSavings = 0;

            // Count enabled optimizations
            $('.webp-checkbox:checked').each(function() {
                enabledCount++;

                // Calculate estimated savings based on option
                var optionName = $(this).attr('name');
                if (optionName && optionName.includes('auto_convert_uploads')) {
                    estimatedSavings += 30; // 30% average savings for new uploads
                } else if (optionName && optionName.includes('replace_in_content')) {
                    estimatedSavings += 25; // 25% average savings for content replacement
                } else if (optionName && optionName.includes('backup_originals')) {
                    // Backup doesn't add savings, but adds safety
                }
            });

            // Cap savings at reasonable maximum
            estimatedSavings = Math.min(estimatedSavings, 35);

            // Update summary display
            $('.webp-summary .enabled-count').text(enabledCount + ' optimizations enabled');
            $('.webp-summary .estimated-savings').text('Estimated savings: ' + estimatedSavings + '%');

            // Update summary styling based on enabled count
            var summaryStats = $('.webp-summary .summary-stats');
            summaryStats.removeClass('low-optimization medium-optimization high-optimization');

            if (enabledCount === 0) {
                summaryStats.addClass('low-optimization');
            } else if (enabledCount <= 2) {
                summaryStats.addClass('medium-optimization');
            } else {
                summaryStats.addClass('high-optimization');
            }
        },

        // Format file size
        formatFileSize: function(bytes) {
            if (bytes === 0) return '0 B';

            var k = 1024;
            var sizes = ['B', 'KB', 'MB', 'GB'];
            var i = Math.floor(Math.log(bytes) / Math.log(k));

            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        console.log('🔧 WebP Admin JS Document Ready');
        console.log('🔧 Current URL:', window.location.href);

        // Multiple detection methods for better reliability
        const isWebPPage = window.location.href.indexOf('tab=smart-webp-conversion') !== -1 ||
                          $('.redco-module-tab[data-module="smart-webp-conversion"]').length > 0 ||
                          $('#test-webp-modal').length > 0;

        console.log('🔧 WebP page detected:', isWebPPage);

        if (isWebPPage) {
            console.log('🔧 Initializing WebP Admin...');
            WebPAdmin.init();
            console.log('✅ WebP Admin initialized');
        } else {
            console.log('⚠️ Not on WebP module page, skipping initialization');
        }
    });

    // Make WebPAdmin globally available
    window.WebPAdmin = WebPAdmin;

    // Fallback initialization - try again after a short delay
    setTimeout(function() {
        if (typeof window.WebPAdmin !== 'undefined' && !window.WebPAdmin.initialized) {
            console.log('🔧 Fallback initialization attempt...');
            if ($('#test-webp-modal').length > 0) {
                console.log('🔧 WebP elements found, initializing...');
                WebPAdmin.init();
                WebPAdmin.initialized = true;
            }
        }
    }, 1000);

})(jQuery);


