/**
 * Smart WebP Conversion Admin JavaScript
 * 
 * @package Redco_Optimizer
 * @subpackage Smart_WebP_Conversion
 * @since 1.0.0
 */

(function($) {
    'use strict';
    
    var WebPAdmin = {
        
        // Properties
        isConverting: false,
        conversionData: {
            totalImages: 0,
            processedImages: 0,
            errorCount: 0,
            totalSavings: 0,
            currentBatch: 0,
            batchSize: 10
        },
        
        // Initialize
        init: function() {
            this.bindEvents();
            this.updateStats();
        },
        
        // Bind event handlers
        bindEvents: function() {
            // Bulk conversion button
            $('#bulk-convert-images').on('click', this.startBulkConversion.bind(this));
            
            // Test server support button
            $('#test-webp-support').on('click', this.testServerSupport.bind(this));
            
            // Modal close buttons
            $('#close-conversion-modal, #cancel-conversion').on('click', this.closeModal.bind(this));
            
            // Quality slider
            $('#quality').on('input', function() {
                $('.quality-value').text($(this).val() + '%');
            });
            
            // Lossless toggle
            $('#lossless').on('change', function() {
                if ($(this).is(':checked')) {
                    $('#quality-setting').slideUp();
                } else {
                    $('#quality-setting').slideDown();
                }
            });
            
            // Auto-refresh stats every 30 seconds
            setInterval(this.updateStats.bind(this), 30000);
        },
        
        // Start bulk conversion process
        startBulkConversion: function() {
            if (this.isConverting) {
                return;
            }
            
            // Reset conversion data
            this.conversionData = {
                totalImages: 0,
                processedImages: 0,
                errorCount: 0,
                totalSavings: 0,
                currentBatch: 0,
                batchSize: parseInt($('#batch_size').val()) || 10
            };
            
            // Show modal
            this.showModal();
            this.isConverting = true;
            
            // Start conversion
            this.processBatch();
        },
        
        // Process a batch of images
        processBatch: function() {
            var self = this;
            
            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_bulk_convert',
                    nonce: redcoWebP.nonces.bulk_convert,
                    batch_size: self.conversionData.batchSize,
                    offset: self.conversionData.currentBatch * self.conversionData.batchSize,
                    total_processed: self.conversionData.processedImages
                },
                success: function(response) {
                    if (response.success) {
                        self.handleBatchSuccess(response);
                    } else {
                        self.handleError('Batch conversion failed');
                    }
                },
                error: function() {
                    self.handleError('Network error during conversion');
                }
            });
        },
        
        // Handle successful batch conversion
        handleBatchSuccess: function(response) {
            // Update conversion data
            this.conversionData.processedImages = response.total_processed;
            this.conversionData.errorCount += response.errors.length;
            
            // Calculate total savings
            var batchSavings = 0;
            response.conversions.forEach(function(conversion) {
                batchSavings += conversion.savings;
            });
            this.conversionData.totalSavings += batchSavings;
            
            // Update progress display
            this.updateProgress(response);
            
            // Log conversions
            this.logConversions(response.conversions, response.errors);
            
            // Continue with next batch if there are more images
            if (response.has_more && this.isConverting) {
                this.conversionData.currentBatch++;
                setTimeout(this.processBatch.bind(this), 1000); // Small delay between batches
            } else {
                this.completeConversion();
            }
        },
        
        // Update progress display
        updateProgress: function(response) {
            var processed = this.conversionData.processedImages;
            var total = processed + (response.has_more ? this.conversionData.batchSize : 0);
            
            // Update progress bar
            var percentage = total > 0 ? Math.round((processed / total) * 100) : 0;
            $('.progress-fill').css('width', percentage + '%');
            
            // Update progress text
            $('.progress-current').text(processed);
            $('.progress-total').text(total);
            
            // Update stats
            $('.processed-count').text(processed);
            $('.error-count').text(this.conversionData.errorCount);
            $('.total-savings').text(this.formatFileSize(this.conversionData.totalSavings));
        },
        
        // Log conversion results
        logConversions: function(conversions, errors) {
            var logContainer = $('.log-entries');
            
            // Log successful conversions
            conversions.forEach(function(conversion) {
                var logEntry = $('<div class="log-entry log-success">')
                    .html('<span class="dashicons dashicons-yes"></span> ' + 
                          conversion.title + ' - Saved ' + 
                          this.formatFileSize(conversion.savings));
                logContainer.append(logEntry);
            }.bind(this));
            
            // Log errors
            errors.forEach(function(error) {
                var logEntry = $('<div class="log-entry log-error">')
                    .html('<span class="dashicons dashicons-no"></span> ' + 
                          error.title + ' - ' + error.error);
                logContainer.append(logEntry);
            });
            
            // Scroll to bottom
            logContainer.scrollTop(logContainer[0].scrollHeight);
        },
        
        // Complete conversion process
        completeConversion: function() {
            this.isConverting = false;
            
            // Update modal
            $('.modal-header h2').text(redcoWebP.strings.complete);
            $('#cancel-conversion').hide();
            $('#close-conversion-modal').show();
            
            // Update stats
            this.updateStats();
            
            // Show completion message
            var message = 'Conversion completed! Processed ' + 
                         this.conversionData.processedImages + ' images';
            
            if (this.conversionData.totalSavings > 0) {
                message += ', saved ' + this.formatFileSize(this.conversionData.totalSavings);
            }
            
            if (this.conversionData.errorCount > 0) {
                message += ' (' + this.conversionData.errorCount + ' errors)';
            }
            
            $('.current-file').html('<strong>' + message + '</strong>');
        },
        
        // Test server support
        testServerSupport: function() {
            var button = $('#test-webp-support');
            var originalText = button.text();
            
            button.prop('disabled', true).text('Testing...');
            
            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_test_conversion',
                    nonce: redcoWebP.nonces.test
                },
                success: function(response) {
                    button.prop('disabled', false).text(originalText);
                    
                    if (response.success) {
                        alert('✓ ' + response.message);
                    } else {
                        alert('✗ ' + response.message);
                    }
                    
                    // Log capabilities to console
                    console.log('WebP Capabilities:', response.capabilities);
                },
                error: function() {
                    button.prop('disabled', false).text(originalText);
                    alert('Error testing server support');
                }
            });
        },
        
        // Update statistics
        updateStats: function() {
            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_get_stats',
                    nonce: redcoWebP.nonces.stats
                },
                success: function(response) {
                    if (response.success !== false) {
                        // Update header metrics
                        $('.metric-card').eq(0).find('.metric-value').text(response.converted_images.toLocaleString());
                        $('.metric-card').eq(1).find('.metric-value').text(response.conversion_percentage + '%');
                        $('.metric-card').eq(2).find('.metric-value').text(WebPAdmin.formatFileSize(response.total_savings));
                        
                        // Update sidebar stats
                        $('.stat-item').eq(0).find('.stat-value').text(response.total_images.toLocaleString());
                        $('.stat-item').eq(1).find('.stat-value').text(response.converted_images.toLocaleString());
                        $('.stat-item').eq(2).find('.stat-value').text(response.unconverted_images.toLocaleString());
                        $('.stat-item').eq(3).find('.stat-value').text(WebPAdmin.formatFileSize(response.total_savings));
                        $('.stat-item').eq(4).find('.stat-value').text(response.savings_percentage + '%');
                    }
                }
            });
        },
        
        // Show conversion modal
        showModal: function() {
            $('#webp-progress-modal').show();
            
            // Reset modal content
            $('.progress-fill').css('width', '0%');
            $('.progress-current').text('0');
            $('.progress-total').text('0');
            $('.processed-count').text('0');
            $('.error-count').text('0');
            $('.total-savings').text('0 KB');
            $('.log-entries').empty();
            $('.current-file').text('Preparing conversion...');
            
            // Show/hide buttons
            $('#cancel-conversion').show();
            $('#close-conversion-modal').hide();
        },
        
        // Close modal
        closeModal: function() {
            if (this.isConverting) {
                if (confirm('Are you sure you want to cancel the conversion?')) {
                    this.isConverting = false;
                    $('#webp-progress-modal').hide();
                }
            } else {
                $('#webp-progress-modal').hide();
            }
        },
        
        // Handle errors
        handleError: function(message) {
            this.isConverting = false;
            $('.current-file').html('<span style="color: #d63638;"><strong>Error: ' + message + '</strong></span>');
            $('#cancel-conversion').hide();
            $('#close-conversion-modal').show();
        },
        
        // Format file size
        formatFileSize: function(bytes) {
            if (bytes === 0) return '0 B';
            
            var k = 1024;
            var sizes = ['B', 'KB', 'MB', 'GB'];
            var i = Math.floor(Math.log(bytes) / Math.log(k));
            
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }
    };
    
    // Initialize when document is ready
    $(document).ready(function() {
        WebPAdmin.init();
    });
    
    // Make WebPAdmin globally available
    window.WebPAdmin = WebPAdmin;
    
})(jQuery);
