/**
 * Smart WebP Conversion Admin JavaScript
 *
 * @package Redco_Optimizer
 * @subpackage Smart_WebP_Conversion
 * @since 1.0.0
 */

(function($) {
    'use strict';

    var WebPAdmin = {

        // Properties
        isConverting: false,
        conversionData: {
            totalImages: 0,
            processedImages: 0,
            errorCount: 0,
            totalSavings: 0,
            currentBatch: 0,
            batchSize: 10
        },

        // Initialize
        init: function() {
            this.bindEvents();
            this.updateStats();
        },

        // Bind event handlers
        bindEvents: function() {
            console.log('🔧 WebP Admin binding events...');

            // Bulk conversion button
            const $bulkButton = $('#bulk-convert-images');
            console.log('🔍 Binding to button:', $bulkButton.length > 0 ? 'Found' : 'NOT FOUND');

            // Use event delegation for better compatibility
            $(document).on('click', '#bulk-convert-images', function(e) {
                console.log('🎯 WebP bulk convert button clicked via delegation!');
                e.preventDefault();

                const $button = $(this);

                // Check if button is disabled
                if ($button.prop('disabled')) {
                    console.log('⚠️ Button is disabled - server may not support WebP');
                    alert('WebP conversion is not available. Your server may not support WebP conversion. Please contact your hosting provider to enable GD library with WebP support.');
                    return;
                }

                console.log('🚀 Calling WebPAdmin.startBulkConversion()...');
                WebPAdmin.startBulkConversion();
            });

            if ($bulkButton.length > 0) {
                console.log('✅ Event delegation set up for bulk convert button');

                // Log button state
                if ($bulkButton.prop('disabled')) {
                    console.log('⚠️ Button is disabled - likely due to server WebP support');
                }
            } else {
                console.log('❌ Bulk convert button not found during event binding');
            }

            // Test server support button
            $('#test-webp-support').on('click', this.testServerSupport.bind(this));

            // Modal close buttons
            $('#close-conversion-modal, #cancel-conversion').on('click', this.closeModal.bind(this));

            // Quality slider - update display only, auto-save is handled globally
            $('#quality').on('input', function() {
                $('#quality-value').text($(this).val());
            });

            // Lossless toggle
            $('#lossless').on('change', function() {
                if ($(this).is(':checked')) {
                    $('#quality-setting').slideUp();
                } else {
                    $('#quality-setting').slideDown();
                }
            });

            // WebP checkbox change handlers - use event delegation to avoid conflicts with auto-save
            $(document).on('change', '.webp-checkbox', this.updateWebPSummary.bind(this));

            // Auto-refresh stats every 30 seconds
            setInterval(this.updateStats.bind(this), 30000);

            // Initialize summary
            this.updateWebPSummary();
        },

        // Start bulk conversion process
        startBulkConversion: function() {
            console.log('🚀 startBulkConversion called');

            if (this.isConverting) {
                console.log('⚠️ Conversion already in progress, skipping');
                return;
            }

            console.log('✅ Starting bulk conversion process');

            // Reset conversion data
            this.conversionData = {
                totalImages: 0,
                processedImages: 0,
                errorCount: 0,
                totalSavings: 0,
                currentBatch: 0,
                batchSize: parseInt($('#batch_size').val()) || 10
            };

            console.log('📊 Conversion data initialized:', this.conversionData);

            // Show modal
            console.log('🎭 Attempting to show modal...');
            this.showModal();
            this.isConverting = true;

            // Start conversion
            console.log('⚡ Starting batch processing...');
            this.processBatch();
        },

        // Process a batch of images
        processBatch: function() {
            var self = this;

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_bulk_convert',
                    nonce: redcoWebP.nonces.bulk_convert,
                    batch_size: self.conversionData.batchSize,
                    offset: self.conversionData.currentBatch * self.conversionData.batchSize,
                    total_processed: self.conversionData.processedImages
                },
                success: function(response) {
                    if (response.success) {
                        self.handleBatchSuccess(response);
                    } else {
                        self.handleError('Batch conversion failed');
                    }
                },
                error: function() {
                    self.handleError('Network error during conversion');
                }
            });
        },

        // Handle successful batch conversion
        handleBatchSuccess: function(response) {
            // Update conversion data
            this.conversionData.processedImages = response.total_processed;
            this.conversionData.errorCount += response.errors.length;

            // Calculate total savings
            var batchSavings = 0;
            response.conversions.forEach(function(conversion) {
                batchSavings += conversion.savings;
            });
            this.conversionData.totalSavings += batchSavings;

            // Update progress display
            this.updateProgress(response);

            // Log conversions
            this.logConversions(response.conversions, response.errors);

            // Continue with next batch if there are more images
            if (response.has_more && this.isConverting) {
                this.conversionData.currentBatch++;
                setTimeout(this.processBatch.bind(this), 1000); // Small delay between batches
            } else {
                this.completeConversion();
            }
        },

        // Update progress display
        updateProgress: function(response) {
            var processed = this.conversionData.processedImages;
            var total = processed + (response.has_more ? this.conversionData.batchSize : 0);

            // Update progress bar
            var percentage = total > 0 ? Math.round((processed / total) * 100) : 0;
            $('.progress-fill').css('width', percentage + '%');

            // Update progress text
            $('.progress-current').text(processed);
            $('.progress-total').text(total);

            // Update stats
            $('.processed-count').text(processed);
            $('.error-count').text(this.conversionData.errorCount);
            $('.total-savings').text(this.formatFileSize(this.conversionData.totalSavings));
        },

        // Log conversion results
        logConversions: function(conversions, errors) {
            var logContainer = $('.log-entries');

            // Log successful conversions
            conversions.forEach(function(conversion) {
                var logEntry = $('<div class="log-entry log-success">')
                    .html('<span class="dashicons dashicons-yes"></span> ' +
                          conversion.title + ' - Saved ' +
                          this.formatFileSize(conversion.savings));
                logContainer.append(logEntry);
            }.bind(this));

            // Log errors
            errors.forEach(function(error) {
                var logEntry = $('<div class="log-entry log-error">')
                    .html('<span class="dashicons dashicons-no"></span> ' +
                          error.title + ' - ' + error.error);
                logContainer.append(logEntry);
            });

            // Scroll to bottom
            logContainer.scrollTop(logContainer[0].scrollHeight);
        },

        // Complete conversion process
        completeConversion: function() {
            this.isConverting = false;

            // Update modal
            $('.modal-header h2').text(redcoWebP.strings.complete);
            $('#cancel-conversion').hide();
            $('#close-conversion-modal').show();

            // Update stats
            this.updateStats();

            // Show completion message
            var message = 'Conversion completed! Processed ' +
                         this.conversionData.processedImages + ' images';

            if (this.conversionData.totalSavings > 0) {
                message += ', saved ' + this.formatFileSize(this.conversionData.totalSavings);
            }

            if (this.conversionData.errorCount > 0) {
                message += ' (' + this.conversionData.errorCount + ' errors)';
            }

            $('.current-file').html('<strong>' + message + '</strong>');
        },

        // Test server support
        testServerSupport: function() {
            var button = $('#test-webp-support');
            var originalText = button.text();

            button.prop('disabled', true).text('Testing...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_test_conversion',
                    nonce: redcoWebP.nonces.test
                },
                success: function(response) {
                    button.prop('disabled', false).text(originalText);

                    if (response.success) {
                        alert('✓ ' + response.message);
                    } else {
                        alert('✗ ' + response.message);
                    }

                    // Log capabilities to console
                    console.log('WebP Capabilities:', response.capabilities);
                },
                error: function() {
                    button.prop('disabled', false).text(originalText);
                    alert('Error testing server support');
                }
            });
        },

        // Update statistics
        updateStats: function() {
            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_get_stats',
                    nonce: redcoWebP.nonces.stats
                },
                success: function(response) {
                    if (response.success !== false) {
                        // Update header metrics
                        $('.metric-card').eq(0).find('.metric-value').text(response.converted_images.toLocaleString());
                        $('.metric-card').eq(1).find('.metric-value').text(response.conversion_percentage + '%');
                        $('.metric-card').eq(2).find('.metric-value').text(WebPAdmin.formatFileSize(response.total_savings));

                        // Update sidebar stats
                        $('.stat-item').eq(0).find('.stat-value').text(response.total_images.toLocaleString());
                        $('.stat-item').eq(1).find('.stat-value').text(response.converted_images.toLocaleString());
                        $('.stat-item').eq(2).find('.stat-value').text(response.unconverted_images.toLocaleString());
                        $('.stat-item').eq(3).find('.stat-value').text(WebPAdmin.formatFileSize(response.total_savings));
                        $('.stat-item').eq(4).find('.stat-value').text(response.savings_percentage + '%');
                    }
                }
            });
        },

        // Show conversion modal
        showModal: function() {
            console.log('🎭 showModal function called');

            const $modal = $('#webp-progress-modal');
            console.log('🔍 Modal element found:', $modal.length > 0);

            if ($modal.length === 0) {
                console.log('❌ Modal element not found in DOM!');
                alert('Error: Progress modal not found. Please refresh the page and try again.');
                return;
            }

            console.log('✅ Showing modal...');
            $modal.show();

            // Force modal to be visible with inline styles
            $modal.css({
                'display': 'flex',
                'position': 'fixed',
                'top': '0',
                'left': '0',
                'width': '100%',
                'height': '100%',
                'z-index': '999999',
                'background': 'rgba(0,0,0,0.8)'
            });

            // Reset modal content
            console.log('🔄 Resetting modal content...');
            $('.progress-fill').css('width', '0%');
            $('.progress-current').text('0');
            $('.progress-total').text('0');
            $('.processed-count').text('0');
            $('.error-count').text('0');
            $('.total-savings').text('0 KB');
            $('.log-entries').empty();
            $('.current-file').text('Preparing conversion...');

            // Show/hide buttons
            $('#cancel-conversion').show();
            $('#close-conversion-modal').hide();

            console.log('✅ Modal should now be visible');
        },

        // Close modal
        closeModal: function() {
            if (this.isConverting) {
                if (confirm('Are you sure you want to cancel the conversion?')) {
                    this.isConverting = false;
                    $('#webp-progress-modal').hide();
                }
            } else {
                $('#webp-progress-modal').hide();
            }
        },

        // Handle errors
        handleError: function(message) {
            this.isConverting = false;
            $('.current-file').html('<span style="color: #d63638;"><strong>Error: ' + message + '</strong></span>');
            $('#cancel-conversion').hide();
            $('#close-conversion-modal').show();
        },

        // Update WebP settings summary
        updateWebPSummary: function() {
            var enabledCount = 0;
            var estimatedSavings = 0;

            // Count enabled optimizations
            $('.webp-checkbox:checked').each(function() {
                enabledCount++;

                // Calculate estimated savings based on option
                var optionName = $(this).attr('name');
                if (optionName && optionName.includes('auto_convert_uploads')) {
                    estimatedSavings += 30; // 30% average savings for new uploads
                } else if (optionName && optionName.includes('replace_in_content')) {
                    estimatedSavings += 25; // 25% average savings for content replacement
                } else if (optionName && optionName.includes('backup_originals')) {
                    // Backup doesn't add savings, but adds safety
                }
            });

            // Cap savings at reasonable maximum
            estimatedSavings = Math.min(estimatedSavings, 35);

            // Update summary display
            $('.webp-summary .enabled-count').text(enabledCount + ' optimizations enabled');
            $('.webp-summary .estimated-savings').text('Estimated savings: ' + estimatedSavings + '%');

            // Update summary styling based on enabled count
            var summaryStats = $('.webp-summary .summary-stats');
            summaryStats.removeClass('low-optimization medium-optimization high-optimization');

            if (enabledCount === 0) {
                summaryStats.addClass('low-optimization');
            } else if (enabledCount <= 2) {
                summaryStats.addClass('medium-optimization');
            } else {
                summaryStats.addClass('high-optimization');
            }
        },

        // Format file size
        formatFileSize: function(bytes) {
            if (bytes === 0) return '0 B';

            var k = 1024;
            var sizes = ['B', 'KB', 'MB', 'GB'];
            var i = Math.floor(Math.log(bytes) / Math.log(k));

            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        console.log('🔧 WebP Admin JavaScript loading...');

        // Add a visible indicator that JS is loading
        $('body').append('<div id="webp-js-loaded" style="position:fixed;top:10px;right:10px;background:green;color:white;padding:5px;z-index:9999;">WebP JS Loaded</div>');
        setTimeout(function() { $('#webp-js-loaded').remove(); }, 3000);

        // Check if we're on the right page
        if ($('.redco-module-tab[data-module="smart-webp-conversion"]').length === 0) {
            console.log('❌ Not on WebP module page, skipping initialization');
            return;
        }

        // Check if required elements exist
        const $bulkButton = $('#bulk-convert-images');
        console.log('🔍 Bulk convert button found:', $bulkButton.length > 0);

        if ($bulkButton.length > 0) {
            console.log('✅ Button element:', $bulkButton[0]);
            console.log('✅ Button disabled:', $bulkButton.prop('disabled'));
            console.log('✅ Button visible:', $bulkButton.is(':visible'));
        }

        // Check if redcoWebP object exists
        if (typeof redcoWebP !== 'undefined') {
            console.log('✅ redcoWebP object available:', redcoWebP);
        } else {
            console.log('❌ redcoWebP object NOT available');
        }

        WebPAdmin.init();
        console.log('✅ WebP Admin initialized');

        // Test click handler manually
        setTimeout(function() {
            const $testButton = $('#bulk-convert-images');
            if ($testButton.length > 0) {
                console.log('🧪 Testing click handler after 2 seconds...');
                console.log('🧪 Events bound to button:', $._data($testButton[0], 'events'));

                // Add a visual test button
                $testButton.after('<button id="test-webp-click" style="margin-left:10px;background:orange;color:white;border:none;padding:5px;">TEST CLICK</button>');
                $('#test-webp-click').on('click', function() {
                    console.log('🧪 Test button clicked - triggering WebP conversion');
                    alert('Test button clicked! Now triggering WebP conversion...');
                    if (window.WebPAdmin) {
                        WebPAdmin.startBulkConversion();
                    }
                });
            }
        }, 2000);
    });

    // Make WebPAdmin globally available
    window.WebPAdmin = WebPAdmin;

    // Add debugging functions for testing
    window.testWebPButton = function() {
        console.log('🧪 Manual test: Clicking WebP button...');
        const $button = $('#bulk-convert-images');
        if ($button.length > 0) {
            console.log('✅ Button found, triggering click');
            $button.trigger('click');
        } else {
            console.log('❌ Button not found');
        }
    };

    window.testWebPConversion = function() {
        console.log('🧪 Manual test: Starting WebP conversion directly...');
        if (window.WebPAdmin) {
            WebPAdmin.startBulkConversion();
        } else {
            console.log('❌ WebPAdmin not available');
        }
    };

    window.testWebPModal = function() {
        console.log('🧪 Manual test: Testing WebP modal directly...');
        if (window.WebPAdmin) {
            WebPAdmin.showModal();
        } else {
            console.log('❌ WebPAdmin not available');
        }
    };

    window.forceShowWebPModal = function() {
        console.log('🧪 Force showing WebP modal...');
        const $modal = $('#webp-progress-modal');
        if ($modal.length > 0) {
            $modal.css({
                'display': 'flex !important',
                'position': 'fixed',
                'top': '0',
                'left': '0',
                'width': '100%',
                'height': '100%',
                'z-index': '999999',
                'background': 'rgba(0,0,0,0.8)'
            }).show();
            console.log('✅ Modal forced to show');
        } else {
            console.log('❌ Modal not found');
        }
    };

    window.debugWebP = function() {
        console.log('🔍 WebP Debug Info:');
        console.log('- Button exists:', $('#bulk-convert-images').length > 0);
        console.log('- Button disabled:', $('#bulk-convert-images').prop('disabled'));
        console.log('- Button visible:', $('#bulk-convert-images').is(':visible'));
        console.log('- redcoWebP available:', typeof redcoWebP !== 'undefined');
        console.log('- WebPAdmin available:', typeof WebPAdmin !== 'undefined');
        console.log('- Events on button:', $._data($('#bulk-convert-images')[0], 'events'));

        // Test server support
        if (typeof redcoWebP !== 'undefined') {
            console.log('🧪 Testing server WebP support...');
            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_test_conversion',
                    nonce: redcoWebP.nonces.test
                },
                success: function(response) {
                    console.log('✅ Server WebP test result:', response);
                },
                error: function() {
                    console.log('❌ Server WebP test failed');
                }
            });
        }
    };

})(jQuery);
