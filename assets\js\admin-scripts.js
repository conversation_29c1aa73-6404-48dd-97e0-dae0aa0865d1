/**
 * Admin JavaScript for Redco Optimizer
 *
 * Professional UI/UX interactions with enhanced animations and feedback
 */

(function($) {
    'use strict';

    // Configuration
    const config = {
        animationDuration: 300,
        debounceDelay: 500,
        autoSaveDelay: 1500, // Auto-save delay in milliseconds
        toastDuration: 4000,
        performanceUpdateInterval: redcoAjax.settings ? redcoAjax.settings.performanceUpdateInterval : 30000,
        performanceRetryDelay: 5000 // 5 seconds on error
    };

    // Auto-save state management
    let autoSaveTimers = new Map();
    let autoSaveInProgress = new Map();
    let lastSavedData = new Map();

    /**
     * Auto-save functionality for module settings
     */
    function initAutoSave() {
        console.log('🔧 Redco Debug: Initializing auto-save functionality');

        // Listen for changes on all form inputs within module forms
        $(document).on('input change', '.redco-module-form input, .redco-module-form select, .redco-module-form textarea', function() {
            const $input = $(this);
            const $form = $input.closest('.redco-module-form');
            const module = $form.data('module');

            if (!module) {
                console.warn('Redco Debug: No module found for auto-save');
                return;
            }

            // Skip if auto-save is already in progress for this module
            if (autoSaveInProgress.get(module)) {
                console.log(`Redco Debug: Auto-save already in progress for ${module}, skipping`);
                return;
            }

            console.log(`Redco Debug: Input changed in ${module} module, scheduling auto-save`);

            // Clear existing timer for this module
            if (autoSaveTimers.has(module)) {
                clearTimeout(autoSaveTimers.get(module));
            }

            // Set new timer for auto-save
            const timer = setTimeout(() => {
                performAutoSave($form, module);
            }, config.autoSaveDelay);

            autoSaveTimers.set(module, timer);
        });

        // Handle toggle switches specifically (they might not trigger 'input' event)
        $(document).on('change', '.redco-module-form .toggle-switch input[type="checkbox"]', function() {
            const $checkbox = $(this);
            const $form = $checkbox.closest('.redco-module-form');
            const module = $form.data('module');

            if (!module) return;

            console.log(`Redco Debug: Toggle changed in ${module} module, scheduling auto-save`);

            // Clear existing timer
            if (autoSaveTimers.has(module)) {
                clearTimeout(autoSaveTimers.get(module));
            }

            // Set new timer
            const timer = setTimeout(() => {
                performAutoSave($form, module);
            }, config.autoSaveDelay);

            autoSaveTimers.set(module, timer);
        });
    }

    /**
     * Perform auto-save for a specific module
     */
    function performAutoSave($form, module) {
        console.log(`Redco Debug: Performing auto-save for ${module} module`);

        // Mark auto-save as in progress
        autoSaveInProgress.set(module, true);

        // Get form data
        const formData = $form.serializeArray();
        const settings = {};

        // Convert form data to object
        $.each(formData, function(i, field) {
            if (settings[field.name]) {
                // Handle multiple values (checkboxes, multi-select)
                if (!Array.isArray(settings[field.name])) {
                    settings[field.name] = [settings[field.name]];
                }
                settings[field.name].push(field.value);
            } else {
                settings[field.name] = field.value;
            }
        });

        // Check if data has actually changed
        const currentDataString = JSON.stringify(settings);
        const lastDataString = lastSavedData.get(module);

        if (currentDataString === lastDataString) {
            console.log(`Redco Debug: No changes detected for ${module}, skipping auto-save`);
            autoSaveInProgress.set(module, false);
            return;
        }

        // Show auto-save indicator
        showAutoSaveIndicator($form, 'saving');

        // Perform AJAX save
        $.ajax({
            url: redcoAjax.ajaxurl,
            type: 'POST',
            data: {
                action: 'redco_save_module_settings',
                module: module,
                settings: settings,
                nonce: redcoAjax.nonce
            },
            success: function(response) {
                console.log(`Redco Debug: Auto-save success for ${module}:`, response);
                if (response.success) {
                    // Store the successfully saved data
                    lastSavedData.set(module, currentDataString);

                    // Show success indicator
                    showAutoSaveIndicator($form, 'saved');

                    // Hide indicator after delay
                    setTimeout(() => {
                        hideAutoSaveIndicator($form);
                    }, 2000);
                } else {
                    console.error(`Redco Debug: Auto-save failed for ${module}:`, response.data.message);
                    showAutoSaveIndicator($form, 'error');
                    setTimeout(() => {
                        hideAutoSaveIndicator($form);
                    }, 3000);
                }
            },
            error: function(xhr, status, error) {
                console.error(`Redco Debug: Auto-save error for ${module}:`, status, error);
                showAutoSaveIndicator($form, 'error');
                setTimeout(() => {
                    hideAutoSaveIndicator($form);
                }, 3000);
            },
            complete: function() {
                // Mark auto-save as complete
                autoSaveInProgress.set(module, false);
            }
        });
    }

    /**
     * Show auto-save indicator
     */
    function showAutoSaveIndicator($form, state) {
        // Remove existing indicator
        $form.find('.auto-save-indicator').remove();

        let icon, text, className;
        switch (state) {
            case 'saving':
                icon = 'update-alt';
                text = redcoAjax.strings.autoSaving || 'Auto-saving...';
                className = 'saving';
                break;
            case 'saved':
                icon = 'yes-alt';
                text = redcoAjax.strings.autoSaved || 'Auto-saved';
                className = 'saved';
                break;
            case 'error':
                icon = 'warning';
                text = redcoAjax.strings.autoSaveError || 'Auto-save failed';
                className = 'error';
                break;
        }

        const $indicator = $(`
            <div class="auto-save-indicator ${className}">
                <span class="dashicons dashicons-${icon} ${state === 'saving' ? 'spin' : ''}"></span>
                <span class="auto-save-text">${text}</span>
            </div>
        `);

        // Add indicator to form header or top of form
        const $header = $form.find('.card-header').first();
        if ($header.length) {
            $header.append($indicator);
        } else {
            $form.prepend($indicator);
        }

        // Add animation
        setTimeout(() => {
            $indicator.addClass('show');
        }, 10);
    }

    /**
     * Hide auto-save indicator
     */
    function hideAutoSaveIndicator($form) {
        const $indicator = $form.find('.auto-save-indicator');
        $indicator.removeClass('show');
        setTimeout(() => {
            $indicator.remove();
        }, 300);
    }

    // Debug the configuration immediately
    console.log('🔧 Redco Debug: Configuration loaded');
    console.log('🔧 Redco Debug: redcoAjax.settings:', redcoAjax.settings);
    console.log('🔧 Redco Debug: Update interval from settings:', redcoAjax.settings ? redcoAjax.settings.performanceUpdateInterval : 'NOT SET');
    console.log('🔧 Redco Debug: Final config interval:', config.performanceUpdateInterval + 'ms (' + (config.performanceUpdateInterval/1000) + 's)');

    // Performance monitoring state
    let performanceUpdateTimer = null;
    let performanceUpdateActive = false;

    // Initialize when document is ready
    $(document).ready(function() {
        // Debug configuration
        console.log('Redco Debug: Configuration loaded:', config);
        console.log('Redco Debug: AJAX settings:', redcoAjax);

        initializeUI();
        initAutoSave(); // Initialize auto-save functionality
        initModuleActions();
        initSettingsToggles();
        initQuickActions();
        initProgressModalSystem(); // Initialize new professional progress modal system
        initFormHandlers();
        initLicenseHandlers();
        initAddonHandlers();
        initKeyboardShortcuts();
        initPerformanceMonitoringUI(); // Initialize UI only, defer API calls
        initHealthMonitorUI(); // Initialize UI only, defer API calls
        initSettingsChangeListener();
        initPageSpeedRefresh();
        initTabLoadingHandlers();
        initModulePageOptimizations(); // Initialize page height management

        // Hide universal loading screen when page is fully loaded
        initPageLoadDetection();

        // Delay chart initialization to ensure Chart.js is loaded
        setTimeout(initCoreWebVitalsChart, 500);

        // Debug functionality is available via redcoDebug.testAutoUpdate() in console
        // Removed automatic debug call to prevent persistent indicators

        // Make universal loading functions globally available
        window.RedcoUniversalLoading = {
            show: showUniversalLoadingScreen,
            hide: hideUniversalLoadingScreen
        };
    });

    /**
     * Initialize tab loading handlers
     */
    function initTabLoadingHandlers() {
        // Global tab click handler - show loading screen for all module tabs
        $(document).on('click', 'a[href*="tab="]', function(e) {
            const href = $(this).attr('href');
            const currentTab = getUrlParameter('tab') || 'dashboard';

            // Extract target tab from href
            const targetTab = href.match(/tab=([^&]+)/);
            if (targetTab && targetTab[1] !== currentTab) {
                const tabName = targetTab[1];
                showUniversalLoadingScreen(tabName);
            }
        });

        // Also handle navigation menu clicks
        $(document).on('click', '.redco-nav-item a', function(e) {
            const href = $(this).attr('href');
            if (href && href.includes('tab=')) {
                const targetTab = href.match(/tab=([^&]+)/);
                if (targetTab) {
                    const tabName = targetTab[1];
                    showUniversalLoadingScreen(tabName);
                }
            }
        });
    }

    /**
     * Show universal loading screen for any module
     */
    function showUniversalLoadingScreen(moduleName) {
        // Module display names
        const moduleNames = {
            'dashboard': 'Dashboard',
            'modules': 'Modules Overview',
            'diagnostic-autofix': 'Website Diagnostic & Auto-Fix',
            'page-cache': 'Page Cache',
            'lazy-load': 'Lazy Load Images',
            'css-js-minifier': 'CSS/JS Minifier',
            'database-cleanup': 'Database Cleanup',
            'heartbeat-control': 'Heartbeat Control',
            'wordpress-core-tweaks': 'WordPress Core Tweaks',
            'critical-resource-optimizer': 'Critical Resource Optimizer',
            'pagespeed-diagnostic': 'PageSpeed Diagnostic'
        };

        const displayName = moduleNames[moduleName] || 'Module';

        // Create loading overlay HTML
        const loadingHtml = `
            <div id="redco-universal-loading-overlay" class="redco-universal-loading-overlay">
                <div class="redco-universal-loading-content">
                    <div class="redco-universal-loading-spinner">
                        <div class="redco-universal-spinner-ring"></div>
                        <div class="redco-universal-spinner-ring"></div>
                        <div class="redco-universal-spinner-ring"></div>
                    </div>
                    <div class="redco-universal-loading-text">
                        <h3>Loading ${displayName}</h3>
                        <p>Please wait while we prepare your optimization tools...</p>
                        <div class="redco-universal-loading-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing overlay if present
        $('#redco-universal-loading-overlay').remove();

        // Add to body
        $('body').append(loadingHtml);

        // Show with animation
        setTimeout(() => {
            $('#redco-universal-loading-overlay').addClass('show');
        }, 100);

        console.log('🔄 Universal loading screen displayed for:', displayName);
    }

    /**
     * Hide universal loading screen
     */
    function hideUniversalLoadingScreen() {
        const $overlay = $('#redco-universal-loading-overlay');

        if ($overlay.length) {
            $overlay.removeClass('show');

            // Remove from DOM after animation
            setTimeout(() => {
                $overlay.remove();
                console.log('✅ Universal loading screen hidden');
            }, 500);
        }
    }

    /**
     * Get URL parameter value
     */
    function getUrlParameter(name) {
        name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
        const regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
        const results = regex.exec(location.search);
        return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
    }

    /**
     * Initialize page load detection to hide loading screen when content is ready
     */
    function initPageLoadDetection() {
        let checkStartTime = Date.now();
        const maxWaitTime = 10000; // Maximum 10 seconds wait time

        // Wait for all content to be loaded and visible
        function checkContentLoaded() {
            // Check if we've exceeded maximum wait time
            if (Date.now() - checkStartTime > maxWaitTime) {
                console.warn('⚠️ Maximum wait time exceeded, hiding loading screen');
                hideUniversalLoadingScreen();

                // Start deferred API calls even if forced
                initDeferredAPIUpdates();
                return;
            }

            // Check if main content areas are present and visible
            const mainContent = $('.redco-content, .redco-module-content, .redco-module-tab');
            const sidebar = $('.redco-sidebar, .redco-content-sidebar');

            // Check if content is loaded and visible
            const contentLoaded = mainContent.length > 0 && mainContent.is(':visible');
            const imagesLoaded = checkImagesLoaded();

            console.log('Content loading check:', {
                contentLoaded,
                imagesLoaded,
                mainContentCount: mainContent.length,
                currentTab: getUrlParameter('tab') || 'dashboard',
                elapsedTime: Date.now() - checkStartTime + 'ms'
            });

            if (contentLoaded && imagesLoaded) {
                // Additional check for page-specific content
                setTimeout(() => {
                    // Final check that content is fully rendered
                    if (isContentFullyRendered()) {
                        hideUniversalLoadingScreen();
                        console.log('✅ Page content fully loaded, hiding loading screen');

                        // Start deferred API calls after page is fully loaded
                        initDeferredAPIUpdates();
                    } else {
                        console.log('⏳ Content not fully rendered yet, continuing to check...');
                        // Continue checking if content is not fully rendered
                        setTimeout(checkContentLoaded, 500); // Longer delay for AJAX content
                    }
                }, 500); // Longer delay for dashboard AJAX content to load
            } else {
                // Keep checking until content is ready
                setTimeout(checkContentLoaded, 300); // Longer initial check interval
            }
        }

        // Start checking after a brief delay to allow initial rendering
        setTimeout(checkContentLoaded, 300);
    }

    /**
     * Check if all images are loaded
     */
    function checkImagesLoaded() {
        const images = $('img:visible');
        let loadedCount = 0;

        if (images.length === 0) return true;

        images.each(function() {
            if (this.complete && this.naturalHeight !== 0) {
                loadedCount++;
            }
        });

        return loadedCount === images.length;
    }

    /**
     * Check if content is fully rendered and visible
     */
    function isContentFullyRendered() {
        // Get current page/tab
        const currentTab = getUrlParameter('tab') || 'dashboard';

        // Dashboard-specific content indicators
        if (currentTab === 'dashboard') {
            // Check if dashboard containers exist
            const dashboardContainers = [
                '.redco-stats-cards',
                '.redco-performance-overview',
                '.redco-performance-health-dashboard',
                '.redco-dashboard'
            ];

            let hasContainers = false;
            for (let selector of dashboardContainers) {
                if ($(selector).length > 0) {
                    hasContainers = true;
                    break;
                }
            }

            if (!hasContainers) {
                console.log('Dashboard containers not found yet');
                return false;
            }

            // Check if AJAX-loaded content is populated
            const statsCards = $('.redco-stats-cards .stat-card');
            const hasStatsData = statsCards.length > 0 && !statsCards.hasClass('loading');

            // Check for actual data in stats cards (not just placeholders)
            let hasRealStatsData = false;
            statsCards.each(function() {
                const $card = $(this);
                const statNumber = $card.find('.stat-number, .score-number, .metric-number').text().trim();
                // Check if it has real data (not empty, not just dashes or placeholders)
                if (statNumber && statNumber !== '-' && statNumber !== '...' && statNumber !== 'Loading...') {
                    hasRealStatsData = true;
                    return false; // break
                }
            });

            // Check for performance health dashboard data
            const healthDashboard = $('.redco-performance-health-dashboard');
            const hasHealthData = healthDashboard.length > 0 && !healthDashboard.hasClass('loading');

            // Check for actual health scores (not placeholders)
            let hasRealHealthData = false;
            if (hasHealthData) {
                const healthScores = healthDashboard.find('.score-number, .metric-value').filter(':visible');
                healthScores.each(function() {
                    const scoreText = $(this).text().trim();
                    if (scoreText && scoreText !== '-' && scoreText !== '...' && scoreText !== 'Loading...') {
                        hasRealHealthData = true;
                        return false; // break
                    }
                });
            }

            console.log('Dashboard content check:', {
                hasContainers,
                hasStatsData,
                hasRealStatsData,
                statsCardsCount: statsCards.length,
                hasHealthData,
                hasRealHealthData,
                healthDashboardExists: healthDashboard.length > 0
            });

            // Dashboard is ready when we have containers (don't wait for API data)
            // API data will be loaded asynchronously after page load
            return hasContainers;
        }

        // Module-specific content indicators
        const moduleIndicators = [
            '.redco-card:visible',
            '.module-header-section:visible',
            '.redco-settings-section:visible',
            '.redco-module-content:visible',
            '.redco-module-tab:visible',
            '.module-settings-content:visible',
            '.redco-module-settings:visible'
        ];

        // Check for specific module content based on current tab
        let hasModuleContent = false;
        for (let selector of moduleIndicators) {
            const elements = $(selector);
            if (elements.length > 0 && elements.is(':visible')) {
                console.log('Module content found:', selector, 'count:', elements.length);
                hasModuleContent = true;
                break;
            }
        }

        // Additional check for module-specific elements
        if (!hasModuleContent) {
            // Check for settings forms, cards, or other module content
            const additionalSelectors = [
                '.settings-card:visible',
                '.redco-notice:visible',
                '.module-disabled-notice:visible',
                'form.redco-settings-form:visible',
                '.redco-stats-grid:visible'
            ];

            for (let selector of additionalSelectors) {
                if ($(selector).length > 0) {
                    console.log('Additional module content found:', selector);
                    hasModuleContent = true;
                    break;
                }
            }
        }

        // Fallback: check if any substantial content is present
        if (!hasModuleContent) {
            const contentContainers = $('.redco-content, .redco-module-content, .redco-module-tab');
            const hasContent = contentContainers.children().length > 0;
            console.log('Fallback content check:', hasContent, 'containers:', contentContainers.length);
            return hasContent;
        }

        return hasModuleContent;
    }

    /**
     * Initialize UI enhancements
     */
    function initializeUI() {
        // Add toast container for notifications
        $('body').append('<div id="redco-toast-container" class="redco-toast-container"></div>');

        // Auto-hide admin notices after 5 seconds
        setTimeout(function() {
            $('.notice.is-dismissible').fadeOut(300);
        }, 5000);
    }

    /**
     * Initialize module action buttons
     */
    function initModuleActions() {
        // Debug: Log when button initialization starts
        console.log('Redco Debug: Initializing module action buttons...');

        // Handle module action buttons
        $(document).on('click', '.module-action-btn:not(.premium-disabled)', function(e) {
            e.preventDefault();

            const $button = $(this);
            const module = $button.data('module');
            const action = $button.data('action') || $button.attr('data-action');
            const $container = $button.closest('.module-overview-item, .module-card');

            // Debug logging
            console.log('Redco Debug: Module action button clicked:', {
                module: module,
                action: action,
                button: $button,
                container: $container
            });

            // Validate module data
            if (!module || !action) {
                console.error('Redco Debug: Missing module or action data attribute');
                showToast('Error: Module or action not specified', 'error');
                return;
            }

            // Disable button during request
            $button.prop('disabled', true);
            $button.addClass('loading');
            $container.addClass('redco-loading');

            // Add loading spinner
            const originalContent = $button.html();
            $button.html('<span class="dashicons dashicons-update spin"></span> Processing...');

            // Make AJAX request
            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_toggle_module',
                    module: module,
                    module_action: action,
                    nonce: redcoAjax.nonce
                },
                beforeSend: function() {
                    console.log('Redco Debug: Sending AJAX request for module action:', {
                        action: 'redco_toggle_module',
                        module: module,
                        module_action: action
                    });
                },
                success: function(response) {
                    console.log('Redco Debug: AJAX response received:', response);

                    if (response.success) {
                        showToast(response.data.message, 'success');

                        // Update button state immediately
                        const enabled = action === 'enable';
                        updateModuleButtonState($button, enabled, module);

                        // Update container state
                        if (enabled) {
                            $container.addClass('enabled').removeClass('disabled');
                        } else {
                            $container.addClass('disabled').removeClass('enabled');
                        }

                        // Update quick action button states
                        updateQuickActionButtonStates(module, enabled);

                        // Refresh page after short delay to ensure consistency
                        setTimeout(function() {
                            window.location.reload();
                        }, 1500);
                    } else {
                        console.error('Redco Debug: AJAX request failed:', response.data);
                        showToast(response.data.message || redcoAjax.strings.error, 'error');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Redco Debug: AJAX error:', {
                        xhr: xhr,
                        status: status,
                        error: error,
                        responseText: xhr.responseText
                    });
                    showToast(redcoAjax.strings.error || 'Request failed', 'error');
                },
                complete: function() {
                    // Restore button state
                    $button.removeClass('loading');
                    $button.html(originalContent);
                    $container.removeClass('redco-loading');
                    $button.prop('disabled', false);
                }
            });
        });



        // Debug: Check for existing action buttons
        const actionButtons = $('.module-action-btn');
        console.log('Redco Debug: Found module action buttons:', actionButtons.length);

        actionButtons.each(function(index) {
            const $this = $(this);
            const module = $this.data('module');
            const action = $this.data('action');
            console.log(`Redco Debug: Button ${index + 1}:`, {
                module: module,
                action: action,
                disabled: $this.is(':disabled'),
                classes: $this.attr('class')
            });
        });
    }

    /**
     * Update module button state
     */
    function updateModuleButtonState($button, enabled, module) {
        if (enabled) {
            $button.removeClass('disabled').addClass('enabled');
            $button.data('action', 'disable');
            $button.html('<span class="dashicons dashicons-yes-alt"></span> Enabled');
            $button.attr('data-tooltip', 'Click to disable this module');
        } else {
            $button.removeClass('enabled').addClass('disabled');
            $button.data('action', 'enable');
            $button.html('<span class="dashicons dashicons-marker"></span> Disabled');
            $button.attr('data-tooltip', 'Click to enable this module');
        }
    }

    /**
     * Initialize settings toggle switches
     */
    function initSettingsToggles() {
        $(document).on('change', '.settings-toggle', function() {
            const $checkbox = $(this);
            const settingGroup = $checkbox.data('setting-group');
            const settingName = $checkbox.data('setting-name');
            const enabled = $checkbox.is(':checked');
            const $container = $checkbox.closest('.setting-item, .settings-card');

            // Disable toggle during request
            $checkbox.prop('disabled', true);
            $container.addClass('redco-loading');

            // Make AJAX request
            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_toggle_setting',
                    setting_group: settingGroup,
                    setting_name: settingName,
                    enabled: enabled,
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        showToast(response.data.message, 'success');

                        // Update UI state if needed
                        if (settingName === 'enable_monitoring') {
                            // Refresh dashboard if monitoring was toggled
                            if (window.location.href.includes('page=redco-optimizer')) {
                                setTimeout(function() {
                                    window.location.reload();
                                }, 1000);
                            }
                        }
                    } else {
                        // Revert checkbox state
                        $checkbox.prop('checked', !enabled);
                        showToast(response.data.message || redcoAjax.strings.error, 'error');
                    }
                },
                error: function() {
                    // Revert checkbox state
                    $checkbox.prop('checked', !enabled);
                    showToast(redcoAjax.strings.error, 'error');
                },
                complete: function() {
                    $container.removeClass('redco-loading');
                    $checkbox.prop('disabled', false);
                }
            });
        });
    }

    /**
     * Initialize quick action buttons
     */
    function initQuickActions() {
        // Quick action functionality will be implemented here
        // Currently placeholder for future implementation
    }

    /**
     * Professional Progress Modal System
     * Real-time progress tracking with actual server data
     */

    // Progress modal state management
    let currentProgressModal = null;
    let progressUpdateInterval = null;
    let progressSessionId = null;
    let currentActionButton = null;
    let originalButtonText = null;

    /**
     * Initialize professional progress modal system
     */
    function initProgressModalSystem() {
        // Attach handlers to all action buttons
        attachProgressModalHandlers();

        // Setup modal container
        setupProgressModalContainer();

        console.log('✅ Professional Progress Modal System initialized');

        // Safety mechanism: restore button state on page unload
        $(window).on('beforeunload', function() {
            if (currentActionButton && originalButtonText) {
                restoreButtonState();
            }
        });
    }

    /**
     * Setup progress modal container in DOM
     */
    function setupProgressModalContainer() {
        if (!$('#redco-progress-modal-container').length) {
            $('body').append('<div id="redco-progress-modal-container"></div>');
        }
    }

    /**
     * Attach progress modal handlers to action buttons
     */
    function attachProgressModalHandlers() {
        // Dashboard quick action buttons
        $(document).on('click', '.quick-action-btn[data-action]', handleProgressAction);

        // Module-specific action buttons
        $(document).on('click', '#clear-page-cache', function(e) {
            e.preventDefault();
            startProgressAction('clear_page_cache', $(this));
        });

        $(document).on('click', '#clear-minified-cache', function(e) {
            e.preventDefault();
            startProgressAction('clear_minified_cache', $(this));
        });

        $(document).on('click', '#clear-all-cache', function(e) {
            e.preventDefault();
            startProgressAction('clear_all_cache', $(this));
        });

        $(document).on('click', '#run-cleanup-now, #run-database-cleanup', function(e) {
            e.preventDefault();
            startProgressAction('database_cleanup', $(this));
        });

        // Additional specific action button handlers
        $(document).on('click', '#preload-cache', function(e) {
            e.preventDefault();
            startProgressAction('preload_cache', $(this));
        });

        $(document).on('click', '#optimize-database', function(e) {
            e.preventDefault();
            startProgressAction('optimize_database', $(this));
        });

        // Database cleanup variations
        $(document).on('click', '#clean-database, #cleanup-database, #database-cleanup', function(e) {
            e.preventDefault();
            startProgressAction('database_cleanup', $(this));
        });

        // Cache preloading variations
        $(document).on('click', '#preload-cache-now, #cache-preload, #start-preload', function(e) {
            e.preventDefault();
            startProgressAction('preload_cache', $(this));
        });

        // Image optimization
        $(document).on('click', '#optimize-images, #image-optimization', function(e) {
            e.preventDefault();
            startProgressAction('optimize_images', $(this));
        });

        // Asset minification
        $(document).on('click', '#minify-assets, #minify-css-js', function(e) {
            e.preventDefault();
            startProgressAction('minify_assets', $(this));
        });

        // Critical resource optimization
        $(document).on('click', '#optimize-critical-resources', function(e) {
            e.preventDefault();
            startProgressAction('optimize_critical_resources', $(this));
        });

        // Critical CSS generation
        $(document).on('click', '#generate-critical-css', function(e) {
            e.preventDefault();
            startProgressAction('generate_critical_css', $(this));
        });

        // Clear critical cache
        $(document).on('click', '#clear-critical-cache', function(e) {
            e.preventDefault();
            startProgressAction('clear_critical_cache', $(this));
        });

        // Header action buttons
        $(document).on('click', '#apply-recommended-tweaks', function(e) {
            e.preventDefault();
            applyRecommendedTweaks($(this));
        });

        $(document).on('click', '#reset-all-tweaks', function(e) {
            e.preventDefault();
            resetAllTweaks($(this));
        });

        // Generic action buttons with data attributes
        $(document).on('click', '[data-redco-action]', function(e) {
            e.preventDefault();
            const action = $(this).data('redco-action');
            if (action) {
                startProgressAction(action, $(this));
            }
        });

        // Catch-all for any remaining action buttons
        $(document).on('click', '[id*="clear-"], [id*="run-"], [id*="optimize-"], [id*="preload-"], [id*="clean-"], [id*="minify-"], [id*="compress-"], [id*="critical-"], [id*="generate-"]', function(e) {
            const $button = $(this);
            const buttonId = $button.attr('id');

            // Skip if already handled by specific handlers above
            const handledButtons = [
                'clear-page-cache', 'clear-minified-cache', 'clear-all-cache',
                'run-cleanup-now', 'run-database-cleanup', 'preload-cache',
                'optimize-database', 'clean-database', 'cleanup-database',
                'database-cleanup', 'preload-cache-now', 'cache-preload',
                'start-preload', 'optimize-images', 'image-optimization',
                'minify-assets', 'minify-css-js', 'optimize-critical-resources',
                'generate-critical-css', 'clear-critical-cache'
            ];

            if (handledButtons.includes(buttonId)) {
                return;
            }

            // Skip if it's not an action button (e.g., form elements)
            if ($button.is('input[type="submit"]') || $button.closest('form').length) {
                return;
            }

            // Try to determine action from button ID or data attributes
            let action = $button.data('action') || $button.data('redco-action');

            if (!action) {
                // Map common button ID patterns to actions
                if (buttonId.includes('clear-cache') || buttonId.includes('clear-all')) {
                    action = 'clear_all_cache';
                } else if (buttonId.includes('clear-page')) {
                    action = 'clear_page_cache';
                } else if (buttonId.includes('clear-minified') || buttonId.includes('clear-css') || buttonId.includes('clear-js')) {
                    action = 'clear_minified_cache';
                } else if (buttonId.includes('clear-')) {
                    action = 'clear_cache';
                } else if (buttonId.includes('cleanup') || buttonId.includes('clean-db') || buttonId.includes('optimize-db')) {
                    action = 'database_cleanup';
                } else if (buttonId.includes('preload') || buttonId.includes('warm-cache')) {
                    action = 'preload_cache';
                } else if (buttonId.includes('optimize-images') || buttonId.includes('compress-images')) {
                    action = 'optimize_images';
                } else if (buttonId.includes('minify') || buttonId.includes('compress-assets')) {
                    action = 'minify_assets';
                } else if (buttonId.includes('critical-resources') || buttonId.includes('optimize-critical')) {
                    action = 'optimize_critical_resources';
                } else if (buttonId.includes('generate-critical') || buttonId.includes('critical-css')) {
                    action = 'generate_critical_css';
                } else if (buttonId.includes('optimize')) {
                    action = 'optimize_database';
                }
            }

            if (action) {
                e.preventDefault();
                console.log('🔧 Catch-all handler triggered for button:', buttonId, 'action:', action);
                startProgressAction(action, $(this));
            }
        });
    }

    /**
     * Handle progress action from quick action buttons
     */
    function handleProgressAction(e) {
        e.preventDefault();
        const $button = $(this);
        const action = $button.data('action');

        if (!action) return;

        // Check if module is enabled for module-specific buttons
        const module = $button.data('module');
        if (module && $button.hasClass('disabled')) {
            const moduleName = getModuleName(module);
            showToast(`Please enable the ${moduleName} module first to use this feature.`, 'warning');
            return;
        }

        startProgressAction(action, $button);
    }

    /**
     * Start a progress action with real-time tracking
     */
    function startProgressAction(action, $button) {
        // Prevent multiple simultaneous actions
        if (currentProgressModal) {
            showToast('Another action is already in progress. Please wait for it to complete.', 'warning');
            return;
        }

        // Store button reference and original state
        currentActionButton = $button;
        originalButtonText = $button.text();

        // Disable button and show loading state
        $button.prop('disabled', true).addClass('loading');

        // Generate unique session ID for this operation
        progressSessionId = 'redco_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

        // Get action configuration
        const actionConfig = getActionConfiguration(action);

        // Create and show progress modal
        currentProgressModal = createProgressModal(actionConfig);
        showProgressModal();

        // Start the actual operation
        initiateServerOperation(action, actionConfig);
    }

    /**
     * Get configuration for specific action
     */
    function getActionConfiguration(action) {
        const configs = {
            'clear_page_cache': {
                title: 'Clearing Page Cache',
                description: 'Removing cached HTML pages for fresh content delivery',
                ajaxAction: 'redco_clear_page_cache',
                estimatedDuration: 5000,
                icon: '🗂️'
            },
            'clear_minified_cache': {
                title: 'Clearing Minified Cache',
                description: 'Removing minified CSS and JavaScript files',
                ajaxAction: 'redco_clear_minified_cache',
                estimatedDuration: 3000,
                icon: '⚡'
            },
            'clear_all_cache': {
                title: 'Clearing All Cache',
                description: 'Removing all cached files and data',
                ajaxAction: 'redco_clear_cache',
                estimatedDuration: 8000,
                icon: '🧹'
            },
            'database_cleanup': {
                title: 'Database Optimization',
                description: 'Cleaning and optimizing database for better performance',
                ajaxAction: 'redco_database_cleanup',
                estimatedDuration: 15000,
                icon: '🗄️'
            },
            'preload_cache': {
                title: 'Cache Preloading',
                description: 'Generating cache for faster page loading',
                ajaxAction: 'redco_preload_cache',
                estimatedDuration: 20000,
                icon: '🚀'
            },
            'optimize_database': {
                title: 'Database Optimization',
                description: 'Optimizing database tables and cleaning up data',
                ajaxAction: 'redco_database_cleanup',
                estimatedDuration: 15000,
                icon: '🗄️'
            },
            'clear_cache': {
                title: 'Clearing All Cache',
                description: 'Removing all cached files and data',
                ajaxAction: 'redco_clear_cache',
                estimatedDuration: 8000,
                icon: '🧹'
            },
            'run_cleanup': {
                title: 'Database Cleanup',
                description: 'Cleaning and optimizing database for better performance',
                ajaxAction: 'redco_database_cleanup',
                estimatedDuration: 15000,
                icon: '🗄️'
            },
            'run_database_cleanup': {
                title: 'Database Cleanup',
                description: 'Cleaning and optimizing database for better performance',
                ajaxAction: 'redco_database_cleanup',
                estimatedDuration: 15000,
                icon: '🗄️'
            },
            'database_cleanup': {
                title: 'Database Cleanup',
                description: 'Cleaning and optimizing database for better performance',
                ajaxAction: 'redco_database_cleanup',
                estimatedDuration: 15000,
                icon: '🗄️'
            },
            'preload_cache': {
                title: 'Cache Preloading',
                description: 'Preloading cache for faster page loading',
                ajaxAction: 'redco_preload_cache',
                estimatedDuration: 25000,
                icon: '🚀'
            },
            'optimize_images': {
                title: 'Image Optimization',
                description: 'Optimizing images for better performance',
                ajaxAction: 'redco_optimize_images',
                estimatedDuration: 30000,
                icon: '🖼️'
            },
            'minify_assets': {
                title: 'Asset Minification',
                description: 'Minifying CSS and JavaScript files',
                ajaxAction: 'redco_minify_assets',
                estimatedDuration: 12000,
                icon: '⚡'
            },
            'optimize_critical_resources': {
                title: 'Critical Resource Optimization',
                description: 'Optimizing critical above-the-fold resources for maximum performance',
                ajaxAction: 'redco_optimize_critical_resources',
                estimatedDuration: 20000,
                icon: '🚀'
            },
            'clear_critical_cache': {
                title: 'Clearing Critical Cache',
                description: 'Removing critical CSS files and optimization data',
                ajaxAction: 'redco_clear_critical_cache',
                estimatedDuration: 5000,
                icon: '🗑️'
            },
            'generate_critical_css': {
                title: 'Generating Critical CSS',
                description: 'Creating critical CSS for homepage optimization',
                ajaxAction: 'redco_generate_critical_css',
                estimatedDuration: 8000,
                icon: '⚡'
            }
        };

        return configs[action] || {
            title: 'Processing Action',
            description: 'Performing the requested operation',
            ajaxAction: 'redco_generic_action',
            estimatedDuration: 5000,
            icon: '⚙️'
        };
    }

    /**
     * Create progress modal HTML structure
     */
    function createProgressModal(config) {
        const modalHtml = `
            <div class="redco-progress-overlay" id="redco-progress-overlay">
                <div class="redco-progress-modal">
                    <div class="progress-modal-header">
                        <div class="progress-icon">${config.icon}</div>
                        <div class="progress-title-section">
                            <h3 class="progress-title">${config.title}</h3>
                            <p class="progress-description">${config.description}</p>
                        </div>
                        <button class="progress-close-btn" id="progress-close-btn" style="display: none;" title="Close">
                            <span class="dashicons dashicons-no-alt"></span>
                        </button>
                    </div>

                    <div class="progress-content">
                        <div class="progress-bar-section">
                            <div class="progress-bar-container">
                                <div class="progress-bar" id="progress-bar" style="width: 0%"></div>
                            </div>
                            <div class="progress-percentage" id="progress-percentage">0%</div>
                        </div>

                        <div class="progress-status-section">
                            <div class="current-operation" id="current-operation">Initializing...</div>
                            <div class="operation-details" id="operation-details">Preparing to start operation</div>
                        </div>

                        <div class="progress-stats" id="progress-stats" style="display: none;">
                            <div class="stat-item">
                                <span class="stat-label">Files Processed</span>
                                <span class="stat-value" id="files-processed">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Items Cleaned</span>
                                <span class="stat-value" id="items-cleaned">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Space Saved</span>
                                <span class="stat-value" id="space-saved">0 KB</span>
                            </div>
                        </div>
                    </div>

                    <div class="progress-actions" id="progress-actions" style="display: none;">
                        <button type="button" class="button button-primary" id="progress-done-btn">Done</button>
                    </div>

                    <div class="progress-error" id="progress-error" style="display: none;">
                        <div class="error-icon">⚠️</div>
                        <div class="error-message" id="error-message"></div>
                        <div class="error-actions">
                            <button type="button" class="button button-secondary" id="error-retry-btn">Retry</button>
                            <button type="button" class="button" id="error-close-btn">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        return $(modalHtml);
    }

    /**
     * Show progress modal
     */
    function showProgressModal() {
        if (!currentProgressModal) return;

        // Add modal to container
        $('#redco-progress-modal-container').html(currentProgressModal);

        // Show modal with animation
        setTimeout(() => {
            $('#redco-progress-overlay').addClass('show');
        }, 50);

        // Attach event handlers
        attachModalEventHandlers();
    }

    /**
     * Attach event handlers to modal elements
     */
    function attachModalEventHandlers() {
        // Close button handlers
        $('#progress-close-btn, #progress-done-btn, #error-close-btn').off('click').on('click', hideProgressModal);

        // Retry button handler
        $('#error-retry-btn').off('click').on('click', function() {
            // Hide error section and restart operation
            $('#progress-error').hide();
            $('#progress-content').show();

            // Reset progress display
            $('#progress-bar').css('width', '0%');
            $('#progress-percentage').text('0%');
            $('#current-operation').text('Retrying...');
            $('#operation-details').text('Restarting the operation');
            $('#progress-stats').hide();
            $('#progress-actions').hide();
            $('#progress-close-btn').hide();

            // Note: Full retry implementation would require storing the last action and config
            console.log('🔄 Retry functionality - would restart last operation');
        });

        // Prevent closing during active operation
        $('#redco-progress-overlay').off('click').on('click', function(e) {
            if (e.target === this && $('#progress-close-btn').is(':visible')) {
                hideProgressModal();
            }
        });

        // Keyboard handling
        $(document).off('keydown.progress-modal').on('keydown.progress-modal', function(e) {
            if (e.key === 'Escape' && $('#progress-close-btn').is(':visible')) {
                hideProgressModal();
            }
        });
    }

    /**
     * Hide progress modal
     */
    function hideProgressModal() {
        // Stop progress updates
        if (progressUpdateInterval) {
            clearInterval(progressUpdateInterval);
            progressUpdateInterval = null;
        }

        // Remove keyboard handler
        $(document).off('keydown.progress-modal');

        // Restore button state
        restoreButtonState();

        // Hide modal with animation
        $('#redco-progress-overlay').removeClass('show');

        setTimeout(() => {
            $('#redco-progress-modal-container').empty();
            currentProgressModal = null;
            progressSessionId = null;
        }, 300);
    }

    /**
     * Restore action button to normal state
     */
    function restoreButtonState() {
        if (currentActionButton && originalButtonText) {
            currentActionButton
                .prop('disabled', false)
                .removeClass('loading')
                .text(originalButtonText);

            console.log('✅ Button state restored:', originalButtonText);
        }

        // Clear stored references
        currentActionButton = null;
        originalButtonText = null;
    }

    /**
     * Initiate server operation with real-time tracking
     */
    function initiateServerOperation(action, config) {
        // Prepare AJAX data
        let ajaxData = {
            action: config.ajaxAction,
            nonce: redcoAjax.nonce,
            session_id: progressSessionId,
            track_progress: true
        };

        // Add action-specific data
        if (action === 'database_cleanup') {
            ajaxData.options = getDatabaseCleanupOptions();
        }

        // Start the operation
        $.ajax({
            url: redcoAjax.ajaxurl,
            type: 'POST',
            data: ajaxData,
            timeout: 60000, // 60 second timeout
            success: function(response) {
                console.log('🔧 AJAX Response received:', response);

                if (response.success) {
                    // Update session ID from server response
                    if (response.data && response.data.session_id) {
                        progressSessionId = response.data.session_id;
                        console.log('🔧 Updated session ID from server:', progressSessionId);
                    }

                    // Start progress tracking
                    startProgressTracking(config);

                    // Handle immediate completion or start polling
                    if (response.data.completed) {
                        handleOperationComplete(response.data);
                    }
                } else {
                    console.log('❌ AJAX request failed:', response);
                    showProgressError(response.data.message || 'Operation failed');
                }
            },
            error: function(xhr, status, error) {
                let errorMessage = 'Network error occurred';

                if (xhr.responseJSON && xhr.responseJSON.data && xhr.responseJSON.data.message) {
                    errorMessage = xhr.responseJSON.data.message;
                } else if (status === 'timeout') {
                    errorMessage = 'Operation timed out. Please try again.';
                }

                showProgressError(errorMessage);
            }
        });
    }

    /**
     * Start real-time progress tracking with enhanced error handling
     */
    function startProgressTracking(config) {
        let pollCount = 0;
        const maxPolls = Math.ceil(config.estimatedDuration / 1000); // Poll every second
        const maxRetries = 3;
        let consecutiveErrors = 0;

        console.log(`🔄 Starting progress tracking - Max polls: ${maxPolls}, Session: ${progressSessionId}`);

        progressUpdateInterval = setInterval(() => {
            pollCount++;

            // Safety timeout check first
            if (pollCount >= maxPolls * 2) {
                console.log('⏰ Progress tracking timed out after', pollCount, 'polls');
                clearInterval(progressUpdateInterval);
                progressUpdateInterval = null;
                showProgressError('Operation timed out');
                return;
            }

            // Get progress update from server
            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_get_progress',
                    nonce: redcoAjax.nonce,
                    session_id: progressSessionId
                },
                timeout: 10000, // 10 second timeout
                success: function(response) {
                    consecutiveErrors = 0; // Reset error count on success
                    console.log('📊 Progress tracking response:', response);

                    if (response.success && response.data) {
                        console.log('📊 Progress update:', response.data.percentage + '%', response.data.current_operation);
                        updateProgressDisplay(response.data);

                        // Check if operation is complete
                        if (response.data.completed) {
                            console.log('✅ Operation completed successfully');
                            clearInterval(progressUpdateInterval);
                            progressUpdateInterval = null;
                            handleOperationComplete(response.data);
                        }
                    } else {
                        console.log('⚠️ Invalid progress response:', response);
                        console.log('⚠️ Response success:', response.success);
                        console.log('⚠️ Response data:', response.data);
                        consecutiveErrors++;

                        if (consecutiveErrors >= maxRetries) {
                            clearInterval(progressUpdateInterval);
                            progressUpdateInterval = null;
                            showProgressError('Progress tracking failed - invalid response');
                        }
                    }
                },
                error: function(xhr, status, error) {
                    consecutiveErrors++;
                    console.log(`❌ Progress tracking error ${consecutiveErrors}/${maxRetries}:`, status, error);

                    // Stop polling after max retries or max polls
                    if (consecutiveErrors >= maxRetries || pollCount >= maxPolls) {
                        clearInterval(progressUpdateInterval);
                        progressUpdateInterval = null;

                        let errorMessage = 'Progress tracking failed';
                        if (status === 'timeout') {
                            errorMessage = 'Progress tracking timed out';
                        } else if (pollCount >= maxPolls) {
                            errorMessage = 'Operation timed out';
                        }

                        showProgressError(errorMessage);
                    }
                }
            });

        }, 1000); // Poll every second
    }

    /**
     * Update progress display with real data
     */
    function updateProgressDisplay(data) {
        // Update progress bar
        if (data.percentage !== undefined) {
            $('#progress-bar').css('width', data.percentage + '%');
            $('#progress-percentage').text(Math.round(data.percentage) + '%');
        }

        // Update current operation
        if (data.current_operation) {
            $('#current-operation').text(data.current_operation);
        }

        // Update operation details
        if (data.operation_details) {
            $('#operation-details').text(data.operation_details);
        }

        // Update statistics
        if (data.stats) {
            $('#progress-stats').show();

            if (data.stats.files_processed !== undefined) {
                $('#files-processed').text(data.stats.files_processed.toLocaleString());
            }

            if (data.stats.items_cleaned !== undefined) {
                $('#items-cleaned').text(data.stats.items_cleaned.toLocaleString());
            }

            if (data.stats.space_saved !== undefined) {
                $('#space-saved').text(formatBytes(data.stats.space_saved));
            }
        }
    }

    /**
     * Handle operation completion
     */
    function handleOperationComplete(data) {
        // Update final progress
        $('#progress-bar').css('width', '100%');
        $('#progress-percentage').text('100%');
        $('#current-operation').text('Completed Successfully');
        $('#operation-details').text(data.message || 'Operation completed successfully');

        // Show final statistics
        if (data.stats) {
            updateProgressDisplay(data);
        }

        // Show completion actions
        $('#progress-actions').show();
        $('#progress-close-btn').show();

        // Update dashboard metrics if on dashboard
        setTimeout(() => {
            if (window.location.href.includes('page=redco-optimizer') && !window.location.href.includes('&tab=')) {
                updatePerformanceMetrics();
                updateHealthMetrics();
            }
        }, 1000);

        // Show success toast
        setTimeout(() => {
            showToast(data.message || 'Operation completed successfully', 'success');
        }, 500);
    }

    /**
     * Show progress error with enhanced handling
     */
    function showProgressError(message) {
        console.log('❌ Showing progress error:', message);

        // Clear any running intervals immediately
        if (progressUpdateInterval) {
            clearInterval(progressUpdateInterval);
            progressUpdateInterval = null;
            console.log('🔄 Cleared progress update interval');
        }

        // Hide progress content
        $('#progress-content').hide();

        // Show error section
        $('#progress-error').show();
        $('#error-message').text(message);
        $('#progress-close-btn').show();

        // Add retry button functionality
        $('#progress-retry-btn').off('click').on('click', function() {
            console.log('🔄 Retrying operation...');

            // Hide error and show progress again
            $('#progress-error').hide();
            $('#progress-content').show();

            // Restart the operation if we have the current action
            if (currentActionButton && progressSessionId) {
                const action = currentActionButton.data('action') ||
                             currentActionButton.attr('id')?.replace(/^(clear-|run-|optimize-|preload-)/, '') ||
                             'clear_page_cache';

                console.log('🔄 Restarting action:', action);
                startProgressAction(action, currentActionButton);
            } else {
                showProgressError('Cannot retry - no active operation found');
            }
        });

        // Show error toast
        showToast(message, 'error');

        // Auto-close after 30 seconds if user doesn't interact
        setTimeout(() => {
            if ($('#redco-progress-modal-container').is(':visible') && $('#progress-error').is(':visible')) {
                console.log('🔄 Auto-closing error modal after timeout');
                hideProgressModal();
            }
        }, 30000);
    }

    /**
     * Format bytes to human readable format
     */
    function formatBytes(bytes) {
        if (bytes === 0) return '0 B';

        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }

    /**
     * Get database cleanup options from current settings
     */
    function getDatabaseCleanupOptions() {
        const options = {};

        // Get options from form if available
        if ($('#cleanup-revisions').length) {
            options.cleanup_revisions = $('#cleanup-revisions').is(':checked');
        }
        if ($('#cleanup-spam-comments').length) {
            options.cleanup_spam_comments = $('#cleanup-spam-comments').is(':checked');
        }
        if ($('#cleanup-trashed-posts').length) {
            options.cleanup_trashed_posts = $('#cleanup-trashed-posts').is(':checked');
        }
        if ($('#cleanup-expired-transients').length) {
            options.cleanup_expired_transients = $('#cleanup-expired-transients').is(':checked');
        }

        return options;
    }

    // Make progress modal functions globally accessible for debugging
    window.redcoProgressModal = {
        test: function(action = 'clear_page_cache') {
            console.log('🔧 Testing progress modal for action:', action);
            const $testButton = $('<button>Test Button</button>');
            startProgressAction(action, $testButton);
        },

        testError: function() {
            console.log('🔧 Testing progress modal error state');
            const config = getActionConfiguration('clear_page_cache');
            currentProgressModal = createProgressModal(config);
            showProgressModal();

            setTimeout(() => {
                showProgressError('This is a test error message');
            }, 2000);
        },

        getCurrentSession: function() {
            return {
                sessionId: progressSessionId,
                modalActive: !!currentProgressModal,
                intervalActive: !!progressUpdateInterval
            };
        },

        forceClose: function() {
            console.log('🔧 Force closing progress modal');

            // Clear all intervals
            if (progressUpdateInterval) {
                clearInterval(progressUpdateInterval);
                progressUpdateInterval = null;
                console.log('🔄 Cleared progress update interval');
            }

            // Reset session
            progressSessionId = null;
            currentProgressModal = null;

            // Hide modal
            hideProgressModal();

            // Restore button state
            restoreButtonState();

            // Remove any stuck overlays
            $('.redco-progress-overlay').remove();
            $('#redco-progress-modal-container').remove();

            console.log('✅ Force close completed');
        },

        close: function() {
            console.log('🔧 Closing progress modal normally');
            hideProgressModal();
        },

        retry: function() {
            console.log('🔄 Retrying current operation');
            $('#progress-retry-btn').trigger('click');
        },

        checkButtonState: function() {
            console.log('🔧 Checking current button state:');
            console.log('- Current Action Button:', currentActionButton ? currentActionButton.attr('id') || currentActionButton.text() : 'None');
            console.log('- Original Button Text:', originalButtonText || 'None');
            console.log('- Modal Active:', !!currentProgressModal);

            return {
                hasButton: !!currentActionButton,
                originalText: originalButtonText,
                modalActive: !!currentProgressModal
            };
        },

        restoreButton: function() {
            console.log('🔧 Manually restoring button state');
            restoreButtonState();
        }
    };

    // Make debugging functions globally accessible
    window.redcoDebug = {
        // Professional Progress Modal System Testing
        testProgressModal: function(action = 'clear_page_cache') {
            console.log('🔧 Testing new professional progress modal for action:', action);
            return window.redcoProgressModal.test(action);
        },

        testProgressError: function() {
            console.log('🔧 Testing progress modal error state');
            return window.redcoProgressModal.testError();
        },

        getProgressSession: function() {
            console.log('🔧 Getting current progress session info');
            return window.redcoProgressModal.getCurrentSession();
        },

        forceCloseModal: function() {
            console.log('🔧 Force closing progress modal');
            return window.redcoProgressModal.forceClose();
        },

        testAllActions: function() {
            console.log('🔧 Testing all progress modal actions...');
            const actions = [
                'clear_page_cache',
                'clear_minified_cache',
                'clear_all_cache',
                'database_cleanup',
                'preload_cache',
                'optimize_images',
                'minify_assets',
                'optimize_critical_resources'
            ];

            actions.forEach((action, index) => {
                setTimeout(() => {
                    console.log(`🔧 Testing action ${index + 1}/${actions.length}: ${action}`);
                    this.testProgressModal(action);
                }, index * 3000); // 3 second delay between tests
            });
        },

        testDatabaseCleanup: function() {
            console.log('🔧 Testing database cleanup specifically...');
            this.testProgressModal('database_cleanup');
        },

        testCachePreload: function() {
            console.log('🔧 Testing cache preload specifically...');
            this.testProgressModal('preload_cache');
        },

        testAllCacheOperations: function() {
            console.log('🔧 Testing all cache operations...');
            const cacheActions = ['clear_page_cache', 'clear_minified_cache', 'clear_all_cache', 'preload_cache'];

            cacheActions.forEach((action, index) => {
                setTimeout(() => {
                    console.log(`🔧 Testing cache action ${index + 1}/${cacheActions.length}: ${action}`);
                    this.testProgressModal(action);
                }, index * 4000); // 4 second delay between tests
            });
        },

        testCriticalResourceOptimization: function() {
            console.log('🔧 Testing critical resource optimization specifically...');
            this.testProgressModal('optimize_critical_resources');
        },

        testAllOptimizationActions: function() {
            console.log('🔧 Testing all optimization actions...');
            const optimizationActions = [
                'optimize_critical_resources',
                'optimize_images',
                'minify_assets',
                'database_cleanup'
            ];

            optimizationActions.forEach((action, index) => {
                setTimeout(() => {
                    console.log(`🔧 Testing optimization action ${index + 1}/${optimizationActions.length}: ${action}`);
                    this.testProgressModal(action);
                }, index * 5000); // 5 second delay between tests
            });
        },

        testCriticalResourceOptimizerToggle: function() {
            console.log('🔧 Testing Critical Resource Optimizer module action button...');

            // Find the Critical Resource Optimizer button
            const criticalButton = $('button[data-module="critical-resource-optimizer"]');

            if (criticalButton.length === 0) {
                console.error('❌ Critical Resource Optimizer button not found!');
                console.log('Available module buttons:');
                $('button[data-module]').each(function() {
                    console.log('- ' + $(this).data('module') + ' (action: ' + $(this).data('action') + ')');
                });

                // Also check for legacy toggles
                console.log('Available legacy toggles:');
                $('input[data-module]').each(function() {
                    console.log('- ' + $(this).data('module'));
                });
                return;
            }

            console.log('✅ Found Critical Resource Optimizer button:', criticalButton);
            console.log('Current action:', criticalButton.data('action'));
            console.log('Button classes:', criticalButton.attr('class'));

            // Test button functionality
            const currentAction = criticalButton.data('action');
            console.log(`🔄 Testing ${currentAction} action...`);

            // Simulate click
            criticalButton.trigger('click');

            setTimeout(() => {
                const newAction = criticalButton.data('action');
                console.log('New action after click:', newAction);

                if (newAction !== currentAction) {
                    console.log('✅ Button functionality working correctly');
                } else {
                    console.log('⚠️ Button action did not change - check for issues');
                }
            }, 2000);
        },

        auditCriticalResourceOptimizer: function() {
            console.log('🔍 Auditing Critical Resource Optimizer integration...');

            const checks = {
                moduleButtonExists: false,
                moduleDataAttribute: false,
                buttonActionAttribute: false,
                buttonClass: false,
                ajaxHandlerExists: false,
                moduleInList: false,

            };

            // Check if action button exists
            const button = $('button[data-module="critical-resource-optimizer"]');
            checks.moduleButtonExists = button.length > 0;

            if (checks.moduleButtonExists) {
                checks.moduleDataAttribute = button.data('module') === 'critical-resource-optimizer';
                checks.buttonActionAttribute = button.data('action') !== undefined;
                checks.buttonClass = button.hasClass('module-action-btn');
            }



            // Check if AJAX handler exists
            checks.ajaxHandlerExists = typeof redcoAjax !== 'undefined' && redcoAjax.ajaxurl;

            // Check if module appears in any module lists
            checks.moduleInList = $('[data-module="critical-resource-optimizer"]').length > 0;

            console.log('🔍 Critical Resource Optimizer Audit Results:');
            Object.keys(checks).forEach(check => {
                const status = checks[check] ? '✅' : '❌';
                console.log(`${status} ${check}: ${checks[check]}`);
            });

            // Additional debugging info
            if (checks.moduleButtonExists) {
                console.log('📋 Button Details:');
                console.log('- Action:', button.data('action'));
                console.log('- Classes:', button.attr('class'));
                console.log('- Text:', button.text().trim());
                console.log('- Disabled:', button.is(':disabled'));
            }

            // Check all module buttons
            const allButtons = $('button[data-module]');
            console.log(`📋 Total module buttons found: ${allButtons.length}`);
            allButtons.each(function(index) {
                const $btn = $(this);
                console.log(`- ${index + 1}: ${$btn.data('module')} (${$btn.data('action')})`);
            });

            const allPassed = checks.moduleButtonExists &&
                            checks.moduleDataAttribute &&
                            checks.buttonActionAttribute &&
                            checks.buttonClass &&
                            checks.ajaxHandlerExists &&
                            checks.moduleInList;

            if (allPassed) {
                console.log('🎉 All checks passed! Critical Resource Optimizer button system is working.');
            } else {
                console.log('⚠️ Some checks failed. Review the issues above.');
            }

            return checks;
        },

        debugModuleState: function(module = 'critical-resource-optimizer') {
            console.log(`🔍 Debugging module state for: ${module}`);

            return $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_debug_module_state',
                    module: module,
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    console.log('📊 Module State Debug Results:', response);

                    if (response.success) {
                        const data = response.data;
                        console.log(`Module: ${data.module}`);
                        console.log(`Is Enabled: ${data.is_enabled}`);
                        console.log(`Enabled Modules:`, data.enabled_modules);
                        console.log(`All Options:`, data.all_options);
                        console.log(`Database Check: ${data.database_check}`);
                        console.log(`Modules Enabled Exists: ${data.modules_enabled_exists}`);
                        console.log(`Modules Count: ${data.modules_enabled_count}`);
                        console.log(`Timestamp: ${data.timestamp}`);

                        // Check for potential issues
                        if (!data.database_check) {
                            console.warn('⚠️ No database entry found for redco_optimizer_options');
                        }

                        if (!data.modules_enabled_exists) {
                            console.warn('⚠️ modules_enabled key does not exist in options');
                        }

                        if (data.enabled_modules.length === 0) {
                            console.warn('⚠️ No modules are currently enabled');
                        }
                    } else {
                        console.error('❌ Debug request failed:', response.data);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ AJAX error during debug:', {xhr, status, error});
                }
            });
        },

        testModuleToggle: function(module = 'critical-resource-optimizer', action = 'enable') {
            console.log(`🧪 Testing module toggle: ${module} -> ${action}`);

            return $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_test_module_toggle',
                    module: module,
                    test_action: action,
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    console.log('🧪 Module Toggle Test Results:', response);

                    if (response.success) {
                        const data = response.data;
                        console.log(`Module: ${data.module}`);
                        console.log(`Action: ${data.action}`);
                        console.log(`Was Enabled: ${data.was_enabled}`);
                        console.log(`Is Now Enabled: ${data.is_now_enabled}`);
                        console.log(`Update Result: ${data.update_result}`);
                        console.log(`Verification Passed: ${data.verification_passed}`);

                        if (data.verification_passed) {
                            console.log('✅ Module toggle test PASSED');
                        } else {
                            console.error('❌ Module toggle test FAILED');
                            console.log('Before:', data.enabled_modules_before);
                            console.log('After:', data.enabled_modules_after);
                        }
                    } else {
                        console.error('❌ Toggle test failed:', response.data);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ AJAX error during toggle test:', {xhr, status, error});
                }
            });
        },

        comprehensiveModuleDebug: function(module = 'critical-resource-optimizer') {
            console.log(`🔬 Running comprehensive debug for module: ${module}`);

            // Step 1: Check current state
            this.debugModuleState(module).then(() => {
                console.log('📋 Step 1: Current state checked');

                // Step 2: Test enable
                return this.testModuleToggle(module, 'enable');
            }).then(() => {
                console.log('📋 Step 2: Enable test completed');

                // Step 3: Check state after enable
                return this.debugModuleState(module);
            }).then(() => {
                console.log('📋 Step 3: State after enable checked');

                // Step 4: Test disable
                return this.testModuleToggle(module, 'disable');
            }).then(() => {
                console.log('📋 Step 4: Disable test completed');

                // Step 5: Final state check
                return this.debugModuleState(module);
            }).then(() => {
                console.log('✅ Comprehensive debug completed');
                console.log('🔄 Refreshing page to see UI changes...');

                // Refresh page to see changes
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            }).catch((error) => {
                console.error('❌ Comprehensive debug failed:', error);
            });
        },

        testDirectToggle: function(module = 'critical-resource-optimizer', enabled = true) {
            console.log(`🎯 Testing direct AJAX toggle for: ${module} -> ${enabled ? 'enable' : 'disable'}`);

            return $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_toggle_module',
                    module: module,
                    enabled: enabled,
                    nonce: redcoAjax.nonce
                },
                beforeSend: function() {
                    console.log('🚀 Sending direct toggle request...');
                },
                success: function(response) {
                    console.log('✅ Direct toggle response:', response);

                    if (response.success) {
                        console.log('🎉 Toggle successful!');
                        console.log('Message:', response.data.message);
                        console.log('Verification:', response.data.verification_success);
                        console.log('Enabled modules:', response.data.enabled_modules);
                    } else {
                        console.error('❌ Toggle failed:', response.data);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ AJAX error:', {
                        status: status,
                        error: error,
                        responseText: xhr.responseText
                    });

                    // Try to parse error response
                    try {
                        const errorResponse = JSON.parse(xhr.responseText);
                        console.error('Parsed error response:', errorResponse);
                    } catch (e) {
                        console.error('Could not parse error response');
                    }
                }
            });
        },

        simulateUIToggle: function(module = 'critical-resource-optimizer') {
            console.log(`🎭 Simulating UI action for: ${module}`);

            // Find the action button first
            const button = $(`button[data-module="${module}"]`);

            if (button.length > 0) {
                console.log('✅ Action button found:', button);
                console.log('Current action:', button.data('action'));
                console.log('Button classes:', button.attr('class'));

                // Simulate the click event
                console.log('🖱️ Simulating button click...');
                button.trigger('click');

                // Check if the action changed
                setTimeout(() => {
                    console.log('Action after click:', button.data('action'));
                    console.log('Button classes after click:', button.attr('class'));
                }, 2000);
                return;
            }

            console.error(`❌ Action button not found for module: ${module}`);
            console.log('Available buttons:');
            $('button[data-module]').each(function() {
                console.log('- ' + $(this).data('module'));
            });
            return;
        },

        // Button state testing functions
        checkButtonState: function() {
            console.log('🔧 Checking button state management');
            return window.redcoProgressModal.checkButtonState();
        },

        testButtonRestore: function() {
            console.log('🔧 Testing button state restoration');

            // Create a test button
            const $testBtn = $('<button id="test-btn" class="button">Test Button</button>');
            $('body').append($testBtn);

            // Simulate button click
            console.log('📝 Original button state:', $testBtn.text(), 'disabled:', $testBtn.prop('disabled'));

            // Start action
            startProgressAction('clear_page_cache', $testBtn);

            setTimeout(() => {
                console.log('📝 Button state during action:', $testBtn.text(), 'disabled:', $testBtn.prop('disabled'));

                // Force close modal
                hideProgressModal();

                setTimeout(() => {
                    console.log('📝 Button state after close:', $testBtn.text(), 'disabled:', $testBtn.prop('disabled'));
                    $testBtn.remove();
                }, 500);
            }, 2000);
        },

        forceRestoreButton: function() {
            console.log('🔧 Force restoring button state');
            return window.redcoProgressModal.restoreButton();
        },

        // Comprehensive button integration testing
        auditActionButtons: function() {
            console.log('🔍 Auditing all action buttons for progress modal integration...');
            console.log('='.repeat(60));

            const buttonSelectors = [
                '#clear-page-cache',
                '#clear-minified-cache',
                '#clear-all-cache',
                '#run-cleanup-now',
                '#run-database-cleanup',
                '#preload-cache',
                '#optimize-database',
                '#clean-database',
                '#cleanup-database',
                '#database-cleanup',
                '#preload-cache-now',
                '#cache-preload',
                '#start-preload',
                '#optimize-images',
                '#image-optimization',
                '#minify-assets',
                '#minify-css-js',
                '#optimize-critical-resources',
                '#generate-critical-css',
                '#clear-critical-cache',
                '.quick-action-btn[data-action]',
                '[data-redco-action]',
                '[id*="clear-"]',
                '[id*="run-"]',
                '[id*="optimize-"]',
                '[id*="preload-"]',
                '[id*="clean-"]',
                '[id*="minify-"]',
                '[id*="compress-"]',
                '[id*="critical-"]',
                '[id*="generate-"]'
            ];

            let totalButtons = 0;
            let integratedButtons = 0;

            buttonSelectors.forEach(selector => {
                const $buttons = $(selector);
                if ($buttons.length > 0) {
                    console.log(`📋 Found ${$buttons.length} button(s) matching: ${selector}`);

                    $buttons.each(function() {
                        const $btn = $(this);
                        const id = $btn.attr('id') || 'no-id';
                        const action = $btn.data('action') || $btn.data('redco-action') || 'no-action';
                        const text = $btn.text().trim() || $btn.val() || 'no-text';

                        totalButtons++;

                        // Check if button has proper integration
                        if (action !== 'no-action' || id.includes('clear-') || id.includes('run-') || id.includes('optimize-') || id.includes('preload-')) {
                            integratedButtons++;
                            console.log(`  ✅ ${id}: "${text}" (action: ${action})`);
                        } else {
                            console.log(`  ⚠️ ${id}: "${text}" (no action detected)`);
                        }
                    });
                }
            });

            console.log('='.repeat(60));
            console.log(`📊 Integration Summary:`);
            console.log(`  Total buttons found: ${totalButtons}`);
            console.log(`  Integrated buttons: ${integratedButtons}`);
            console.log(`  Integration rate: ${totalButtons > 0 ? Math.round((integratedButtons / totalButtons) * 100) : 0}%`);

            return {
                total: totalButtons,
                integrated: integratedButtons,
                rate: totalButtons > 0 ? Math.round((integratedButtons / totalButtons) * 100) : 0
            };
        },

        testButtonIntegration: function(buttonSelector = '#clear-page-cache') {
            console.log('🔧 Testing button integration for:', buttonSelector);

            const $button = $(buttonSelector);
            if ($button.length === 0) {
                console.log('❌ Button not found:', buttonSelector);
                return false;
            }

            console.log('📝 Button details:');
            console.log('  - ID:', $button.attr('id') || 'none');
            console.log('  - Text:', $button.text().trim() || $button.val() || 'none');
            console.log('  - data-action:', $button.data('action') || 'none');
            console.log('  - data-redco-action:', $button.data('redco-action') || 'none');
            console.log('  - Classes:', $button.attr('class') || 'none');

            // Simulate click
            console.log('🖱️ Simulating button click...');
            $button.trigger('click');

            return true;
        },

        removeAllConfirmDialogs: function() {
            console.log('🔧 Checking for remaining confirmation dialogs...');

            // Search for confirm() calls in the page
            const scripts = document.querySelectorAll('script');
            let confirmFound = false;

            scripts.forEach(script => {
                if (script.textContent && script.textContent.includes('confirm(')) {
                    console.log('⚠️ Found confirm() call in script:', script.textContent.substring(0, 100) + '...');
                    confirmFound = true;
                }
            });

            if (!confirmFound) {
                console.log('✅ No confirmation dialogs found in page scripts');
            }

            return !confirmFound;
        },

        // Cache debugging functions
        debugCacheDirectories: function() {
            console.log('🔧 Debugging cache directory paths...');

            // Make AJAX call to get cache directory info
            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_debug_cache_info',
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        console.log('📁 Cache Directory Info:', response.data);
                    } else {
                        console.log('❌ Failed to get cache info:', response.data);
                    }
                },
                error: function() {
                    console.log('❌ AJAX error getting cache info');
                }
            });
        },

        createTestCacheFiles: function() {
            console.log('🔧 Creating test cache files...');

            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_create_test_cache',
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        console.log('✅ Test cache files created:', response.data);
                    } else {
                        console.log('❌ Failed to create test cache files:', response.data);
                    }
                },
                error: function() {
                    console.log('❌ AJAX error creating test cache files');
                }
            });
        },

        verifyProgressTracking: function() {
            console.log('🔧 Verifying progress tracking system...');

            // Test with page cache clearing
            this.testProgressModal('clear_page_cache');

            // Monitor progress updates
            setTimeout(() => {
                const session = window.redcoProgressModal.getCurrentSession();
                console.log('📊 Progress Session Status:', session);

                if (session.sessionId) {
                    // Check progress manually
                    $.ajax({
                        url: redcoAjax.ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'redco_get_progress',
                            nonce: redcoAjax.nonce,
                            session_id: session.sessionId
                        },
                        success: function(response) {
                            console.log('📈 Manual Progress Check:', response);
                        }
                    });
                }
            }, 3000);
        },

        // Emergency recovery functions
        emergencyRecovery: function() {
            console.log('🚨 EMERGENCY RECOVERY: Attempting to restore plugin interface...');

            // 1. Force close any stuck modals
            this.forceCloseModal();

            // 2. Clear all intervals
            this.clearAllIntervals();

            // 3. Restore all button states
            this.restoreAllButtons();

            // 4. Remove stuck overlays
            this.removeStuckOverlays();

            // 5. Reset global variables
            this.resetGlobalState();

            console.log('✅ Emergency recovery completed');
        },

        clearAllIntervals: function() {
            console.log('🔧 Clearing all intervals...');

            // Clear progress interval
            if (typeof progressUpdateInterval !== 'undefined' && progressUpdateInterval) {
                clearInterval(progressUpdateInterval);
                progressUpdateInterval = null;
                console.log('✅ Cleared progress update interval');
            }

            // Clear any other intervals that might be running
            for (let i = 1; i < 99999; i++) {
                window.clearInterval(i);
            }

            console.log('✅ All intervals cleared');
        },

        restoreAllButtons: function() {
            console.log('🔧 Restoring all button states...');

            // Find all potentially stuck buttons
            $('button, input[type="submit"], .button').each(function() {
                const $btn = $(this);

                // Re-enable disabled buttons
                if ($btn.prop('disabled')) {
                    $btn.prop('disabled', false);
                    console.log('✅ Re-enabled button:', $btn.attr('id') || $btn.text().trim());
                }

                // Reset button text if it looks like a loading state
                const text = $btn.text();
                if (text.includes('...') || text.includes('Processing') || text.includes('Loading')) {
                    // Try to restore original text
                    const originalText = $btn.data('original-text');
                    if (originalText) {
                        $btn.text(originalText);
                        console.log('✅ Restored button text:', originalText);
                    }
                }
            });

            console.log('✅ All buttons restored');
        },

        removeStuckOverlays: function() {
            console.log('🔧 Removing stuck overlays...');

            // Remove progress modal overlays
            $('.redco-progress-overlay').remove();
            $('#redco-progress-modal-container').remove();

            // Remove any other modal overlays
            $('.modal-overlay, .overlay, .loading-overlay').remove();

            // Re-enable body scrolling
            $('body').removeClass('modal-open').css('overflow', '');

            console.log('✅ All overlays removed');
        },

        resetGlobalState: function() {
            console.log('🔧 Resetting global state...');

            // Reset progress modal variables
            if (typeof progressSessionId !== 'undefined') {
                progressSessionId = null;
            }
            if (typeof currentProgressModal !== 'undefined') {
                currentProgressModal = null;
            }
            if (typeof currentActionButton !== 'undefined') {
                currentActionButton = null;
            }
            if (typeof originalButtonText !== 'undefined') {
                originalButtonText = null;
            }

            console.log('✅ Global state reset');
        },

        // Quick diagnostic function
        diagnoseInterface: function() {
            console.log('🔍 DIAGNOSING INTERFACE STATE...');
            console.log('='.repeat(50));

            // Check modal state
            const modalVisible = $('#redco-progress-modal-container').is(':visible');
            console.log('📱 Progress Modal Visible:', modalVisible);

            // Check intervals
            const hasInterval = !!progressUpdateInterval;
            console.log('⏱️ Progress Interval Active:', hasInterval);

            // Check button states
            const disabledButtons = $('button:disabled, input[type="submit"]:disabled').length;
            console.log('🔘 Disabled Buttons:', disabledButtons);

            // Check overlays
            const overlays = $('.redco-progress-overlay, .modal-overlay, .overlay').length;
            console.log('🎭 Active Overlays:', overlays);

            // Check session
            const hasSession = !!progressSessionId;
            console.log('🔑 Active Session:', hasSession);

            console.log('='.repeat(50));

            // Provide recommendations
            if (modalVisible || hasInterval || disabledButtons > 0 || overlays > 0) {
                console.log('⚠️ ISSUES DETECTED - Run redcoDebug.emergencyRecovery() to fix');
            } else {
                console.log('✅ Interface appears to be functioning normally');
            }

            return {
                modalVisible,
                hasInterval,
                disabledButtons,
                overlays,
                hasSession,
                needsRecovery: modalVisible || hasInterval || disabledButtons > 0 || overlays > 0
            };
        },

        // Clean implementation audit - verify no legacy toggle switches remain
        auditCleanImplementation: function() {
            console.log('🧹 CLEAN IMPLEMENTATION AUDIT');
            console.log('==============================');

            const audit = {
                legacyToggles: $('.redco-toggle-switch, .toggle-switch').length,
                legacyCheckboxes: $('input[type="checkbox"][data-module]').length,
                newButtons: $('button[data-module]').length,
                buttonEventHandlers: 0,
                toggleEventHandlers: 0
            };

            // Check for event handlers
            $('button[data-module]').each(function() {
                const events = $._data(this, 'events');
                if (events && events.click) {
                    audit.buttonEventHandlers++;
                }
            });

            $('.redco-toggle-switch, .toggle-switch').each(function() {
                const events = $._data(this, 'events');
                if (events && (events.change || events.click)) {
                    audit.toggleEventHandlers++;
                }
            });

            console.log('📊 AUDIT RESULTS:');
            console.log(`❌ Legacy toggle switches found: ${audit.legacyToggles} (should be 0)`);
            console.log(`❌ Legacy checkboxes with data-module: ${audit.legacyCheckboxes} (should be 0)`);
            console.log(`✅ New action buttons found: ${audit.newButtons}`);
            console.log(`✅ Button event handlers: ${audit.buttonEventHandlers}`);
            console.log(`❌ Toggle event handlers: ${audit.toggleEventHandlers} (should be 0)`);

            const isClean = audit.legacyToggles === 0 &&
                           audit.legacyCheckboxes === 0 &&
                           audit.toggleEventHandlers === 0 &&
                           audit.newButtons > 0;

            console.log(`\n🎯 IMPLEMENTATION STATUS: ${isClean ? '✅ CLEAN' : '❌ NEEDS CLEANUP'}`);

            if (!isClean) {
                console.log('\n🔧 CLEANUP NEEDED:');
                if (audit.legacyToggles > 0) {
                    console.log('- Remove legacy toggle switch HTML elements');
                    $('.redco-toggle-switch, .toggle-switch').each(function() {
                        console.log('  Found at:', this);
                    });
                }
                if (audit.legacyCheckboxes > 0) {
                    console.log('- Remove legacy checkbox elements with data-module');
                    $('input[type="checkbox"][data-module]').each(function() {
                        console.log('  Found at:', this);
                    });
                }
                if (audit.toggleEventHandlers > 0) {
                    console.log('- Remove legacy toggle event handlers');
                }
            }

            return audit;
        }
    };

    /**
     * Get database cleanup options from current settings
     */
    function getDatabaseCleanupOptions() {
        // Try to get options from the database cleanup form if it exists
        const $dbForm = $('.redco-module-form[data-module="database-cleanup"]');

        if ($dbForm.length) {
            // Form exists, get current form values
            const formData = $dbForm.serializeArray();
            const options = {};

            $.each(formData, function(i, field) {
                if (field.name.startsWith('settings[')) {
                    const key = field.name.replace('settings[', '').replace(']', '');
                    if (options[key]) {
                        if (!Array.isArray(options[key])) {
                            options[key] = [options[key]];
                        }
                        options[key].push(field.value);
                    } else {
                        options[key] = field.value;
                    }
                }
            });

            return options;
        } else {
            // Form not available, use default safe options
            return {
                cleanup_revisions: '1',
                keep_revisions: '5',
                cleanup_auto_drafts: '1',
                cleanup_trashed_posts: '1',
                cleanup_spam_comments: '1',
                cleanup_trashed_comments: '1',
                cleanup_expired_transients: '1',
                cleanup_orphaned_postmeta: '1',
                cleanup_orphaned_commentmeta: '1'
            };
        }
    }

    /**
     * Get module display name
     */
    function getModuleName(moduleKey) {
        const moduleNames = {
            'page-cache': 'Page Cache',
            'database-cleanup': 'Database Cleanup',
            'lazy-load': 'Lazy Load',
            'css-js-minifier': 'CSS/JS Minifier',
            'heartbeat-control': 'Heartbeat Control',
            'wordpress-core-tweaks': 'WordPress Core Tweaks',
            'critical-resource-optimizer': 'Critical Resource Optimizer'
        };

        return moduleNames[moduleKey] || moduleKey;
    }

    /**
     * Update quick action button states based on module status
     */
    function updateQuickActionButtonStates(moduleKey, enabled) {
        $('.header-quick-actions .quick-action-btn').each(function() {
            const $button = $(this);
            const buttonModule = $button.data('module');

            if (buttonModule === moduleKey) {
                if (enabled) {
                    // Enable the button
                    $button.removeClass('disabled').prop('disabled', false);
                    $button.css('pointer-events', 'auto');

                    // Update tooltip
                    const action = $button.data('action');
                    let newTooltip = '';
                    switch (action) {
                        case 'clear_cache':
                            newTooltip = 'Clear all cache files';
                            break;
                        case 'optimize_database':
                            newTooltip = 'Clean and optimize database';
                            break;
                        case 'preload_cache':
                            newTooltip = 'Preload cache for faster loading';
                            break;
                    }
                    $button.attr('data-tooltip', newTooltip);
                } else {
                    // Disable the button
                    $button.addClass('disabled').prop('disabled', true);
                    $button.css('pointer-events', 'none');

                    // Update tooltip
                    const moduleName = getModuleName(moduleKey);
                    $button.attr('data-tooltip', `Enable ${moduleName} module to use this feature`);
                }
            }
        });
    }

    /**
     * Update all quick action button states
     */
    function updateAllQuickActionStates() {
        $('.header-quick-actions .quick-action-btn').each(function() {
            const $button = $(this);
            const module = $button.data('module');

            // Check if module is enabled (this would need to be passed from PHP or fetched via AJAX)
            // For now, we'll rely on the initial PHP rendering
        });
    }

    /**
     * Initialize form handlers
     */
    function initFormHandlers() {
        // Module settings forms - Use event delegation for dynamic forms
        $(document).on('submit', '.redco-module-form', function(e) {
            e.preventDefault();

            const $form = $(this);
            const module = $form.data('module');

            if (!module) {
                console.error('Redco Debug: No module specified for form');
                showToast('Error: No module specified', 'error');
                return;
            }

            console.log('Redco Debug: Submitting form for module:', module);

            const formData = $form.serializeArray();

            // Convert form data to object
            const settings = {};
            $.each(formData, function(i, field) {
                if (settings[field.name]) {
                    // Handle multiple values (checkboxes, multi-select)
                    if (!Array.isArray(settings[field.name])) {
                        settings[field.name] = [settings[field.name]];
                    }
                    settings[field.name].push(field.value);
                } else {
                    settings[field.name] = field.value;
                }
            });

            console.log('Redco Debug: Form data:', settings);

            // Show saving state
            const $submitBtn = $form.find('input[type="submit"], button[type="submit"]');
            if (!$submitBtn.length) {
                console.error('Redco Debug: No submit button found in form');
                showToast('Error: No submit button found', 'error');
                return;
            }

            const originalText = $submitBtn.val() || $submitBtn.text();
            const loadingText = redcoAjax.strings.saving || 'Saving...';

            $submitBtn.prop('disabled', true);

            // Handle both input and button elements
            if ($submitBtn.is('input[type="submit"]')) {
                $submitBtn.val(loadingText);
            } else {
                $submitBtn.text(loadingText);
            }

            // Add loading class to form
            $form.addClass('redco-loading');

            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_save_module_settings',
                    module: module,
                    settings: settings,
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    console.log('Redco Debug: Save response:', response);
                    if (response.success) {
                        showToast(response.data.message || redcoAjax.strings.saved || 'Settings saved successfully!', 'success');

                        // Briefly show success state
                        const successText = redcoAjax.strings.saved || 'Saved!';
                        if ($submitBtn.is('input[type="submit"]')) {
                            $submitBtn.val(successText);
                        } else {
                            $submitBtn.text(successText);
                        }

                        // Reset button after 2 seconds
                        setTimeout(function() {
                            if ($submitBtn.is('input[type="submit"]')) {
                                $submitBtn.val(originalText);
                            } else {
                                $submitBtn.text(originalText);
                            }
                        }, 2000);
                    } else {
                        showToast(response.data.message || redcoAjax.strings.error || 'Error saving settings', 'error');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Redco Debug: Save error:', status, error);
                    showToast(redcoAjax.strings.error || 'Error saving settings', 'error');
                },
                complete: function() {
                    $form.removeClass('redco-loading');
                    $submitBtn.prop('disabled', false);

                    // Only reset text if it's not showing success state
                    setTimeout(function() {
                        const currentText = $submitBtn.is('input[type="submit"]') ? $submitBtn.val() : $submitBtn.text();
                        const successText = redcoAjax.strings.saved || 'Saved!';

                        if (currentText !== successText) {
                            if ($submitBtn.is('input[type="submit"]')) {
                                $submitBtn.val(originalText);
                            } else {
                                $submitBtn.text(originalText);
                            }
                        }
                    }, 100);
                }
            });
        });
    }

    /**
     * Initialize license handlers
     */
    function initLicenseHandlers() {
        // Activate license
        $('#activate-license').on('click', function() {
            const $button = $(this);
            const licenseKey = $('#license_key').val().trim();

            if (!licenseKey) {
                showNotice('Please enter a license key', 'error');
                return;
            }

            $button.prop('disabled', true).text('Activating...');

            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_activate_license',
                    license_key: licenseKey,
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        showNotice(response.data.message, 'success');
                        setTimeout(function() {
                            window.location.reload();
                        }, 1000);
                    } else {
                        showNotice(response.data.message, 'error');
                    }
                },
                error: function() {
                    showNotice('Error activating license', 'error');
                },
                complete: function() {
                    $button.prop('disabled', false).text('Activate License');
                }
            });
        });

        // Deactivate license
        $('#deactivate-license').on('click', function() {
            const $button = $(this);

            // No confirmation needed - provide immediate feedback through progress
            $button.prop('disabled', true).text('Deactivating...');

            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_deactivate_license',
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        showNotice(response.data.message, 'success');
                        setTimeout(function() {
                            window.location.reload();
                        }, 1000);
                    } else {
                        showNotice(response.data.message, 'error');
                    }
                },
                error: function() {
                    showNotice('Error deactivating license', 'error');
                },
                complete: function() {
                    $button.prop('disabled', false).text('Deactivate License');
                }
            });
        });
    }

    /**
     * Initialize addon handlers
     */
    function initAddonHandlers() {
        // Install addon
        $(document).on('click', '.install-addon', function() {
            const $button = $(this);
            const addonSlug = $button.data('addon');

            $button.prop('disabled', true).text('Installing...');

            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_install_addon',
                    addon_slug: addonSlug,
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        showNotice(response.data.message, 'success');
                        setTimeout(function() {
                            window.location.reload();
                        }, 1000);
                    } else {
                        showNotice(response.data.message, 'error');
                    }
                },
                error: function() {
                    showNotice('Error installing addon', 'error');
                },
                complete: function() {
                    $button.prop('disabled', false).text('Install');
                }
            });
        });

        // Activate addon
        $(document).on('click', '.activate-addon', function() {
            const $button = $(this);
            const addonSlug = $button.data('addon');

            $button.prop('disabled', true).text('Activating...');

            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_activate_addon',
                    addon_slug: addonSlug,
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        showNotice(response.data.message, 'success');
                        setTimeout(function() {
                            window.location.reload();
                        }, 1000);
                    } else {
                        showNotice(response.data.message, 'error');
                    }
                },
                error: function() {
                    showNotice('Error activating addon', 'error');
                },
                complete: function() {
                    $button.prop('disabled', false).text('Activate');
                }
            });
        });

        // Deactivate addon
        $(document).on('click', '.deactivate-addon', function() {
            const $button = $(this);
            const addonSlug = $button.data('addon');

            $button.prop('disabled', true).text('Deactivating...');

            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_deactivate_addon',
                    addon_slug: addonSlug,
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        showNotice(response.data.message, 'success');
                        setTimeout(function() {
                            window.location.reload();
                        }, 1000);
                    } else {
                        showNotice(response.data.message, 'error');
                    }
                },
                error: function() {
                    showNotice('Error deactivating addon', 'error');
                },
                complete: function() {
                    $button.prop('disabled', false).text('Deactivate');
                }
            });
        });

        // Update addon
        $(document).on('click', '.update-addon', function() {
            const $button = $(this);
            const addonSlug = $button.data('addon');

            $button.prop('disabled', true).text('Updating...');

            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_update_addon',
                    addon_slug: addonSlug,
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        showNotice(response.data.message, 'success');
                        setTimeout(function() {
                            window.location.reload();
                        }, 1000);
                    } else {
                        showNotice(response.data.message, 'error');
                    }
                },
                error: function() {
                    showNotice('Error updating addon', 'error');
                },
                complete: function() {
                    $button.prop('disabled', false).text('Update');
                }
            });
        });

        // Refresh addons
        $('#refresh-addons').on('click', function() {
            const $button = $(this);

            $button.prop('disabled', true).text('Refreshing...');

            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_refresh_addons',
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        showNotice(response.data.message, 'success');
                        setTimeout(function() {
                            window.location.reload();
                        }, 1000);
                    } else {
                        showNotice(response.data.message, 'error');
                    }
                },
                error: function() {
                    showNotice('Error refreshing addons', 'error');
                },
                complete: function() {
                    $button.prop('disabled', false).text('Refresh List');
                }
            });
        });
    }

    /**
     * Simple toast notification system
     */
    function showToast(message, type = 'info', duration = 3000) {
        const toastId = 'toast-' + Date.now();
        const iconMap = {
            success: '✓',
            error: '✕',
            warning: '⚠',
            info: 'ℹ'
        };

        const $toast = $(`
            <div id="${toastId}" class="redco-toast redco-toast-${type}">
                <div class="redco-toast-icon">${iconMap[type] || iconMap.info}</div>
                <div class="redco-toast-message">${message}</div>
                <button class="redco-toast-close">&times;</button>
            </div>
        `);

        $('#redco-toast-container').append($toast);

        // Show toast
        setTimeout(() => $toast.addClass('show'), 10);

        // Auto-hide
        const hideTimeout = setTimeout(() => hideToast(toastId), duration);

        // Manual close
        $toast.find('.redco-toast-close').on('click', () => {
            clearTimeout(hideTimeout);
            hideToast(toastId);
        });
    }

    /**
     * Hide toast notification
     */
    function hideToast(toastId) {
        const $toast = $('#' + toastId);
        $toast.removeClass('show');
        setTimeout(() => $toast.remove(), 300);
    }

    /**
     * Initialize settings change listener for performance interval updates
     */
    function initSettingsChangeListener() {
        // Listen for performance settings form submissions
        $(document).on('submit', 'form[action*="options.php"]', function(e) {
            const $form = $(this);
            const $intervalSelect = $form.find('select[name="redco_optimizer_performance[update_interval]"]');

            if ($intervalSelect.length) {
                const newInterval = parseInt($intervalSelect.val());
                console.log('🔧 Redco Debug: Performance settings form submitted with interval:', newInterval + 's');

                // Update the interval after a short delay to allow form processing
                setTimeout(function() {
                    updatePerformanceInterval(newInterval);
                }, 2000);
            }
        });

        // Also listen for direct interval changes
        $(document).on('change', 'select[name="redco_optimizer_performance[update_interval]"]', function() {
            const newInterval = parseInt($(this).val());
            console.log('🔧 Redco Debug: Update interval changed to:', newInterval + 's');

            // Show preview of new interval
            showToast(`Auto-update interval will change to ${newInterval} seconds after saving settings`, 'info');
        });
    }



    /**
     * Initialize performance monitoring UI only (no API calls)
     */
    function initPerformanceMonitoringUI() {
        // Only initialize on main dashboard page
        const isMainDashboard = window.location.href.includes('page=redco-optimizer') && !window.location.href.includes('&tab=');
        const hasPerformanceElements = $('.redco-performance-overview, .redco-performance-health-dashboard, .performance-metric, .health-metric').length > 0;

        console.log('Redco Debug: Checking performance monitoring UI conditions');
        console.log('Redco Debug: Is main dashboard:', isMainDashboard);
        console.log('Redco Debug: Has performance elements:', hasPerformanceElements);
        console.log('Redco Debug: Current URL:', window.location.href);

        // Only initialize UI on main dashboard with performance elements
        if (!isMainDashboard || !hasPerformanceElements) {
            console.log('Redco Debug: ❌ Skipping performance monitoring UI - not main dashboard or no performance elements');
            return;
        }

        // Check if monitoring is enabled
        if (redcoAjax.settings && !redcoAjax.settings.monitoringEnabled) {
            console.log('Redco Debug: ❌ Performance monitoring disabled in settings');
            return;
        }

        console.log('Redco Debug: ✅ Initializing performance monitoring UI (API calls deferred)');

        // Add real-time indicator
        addPerformanceIndicator();

        // Handle page visibility changes
        $(document).on('visibilitychange', function() {
            if (document.hidden) {
                console.log('Redco Debug: Page hidden, stopping performance updates');
                stopPerformanceUpdates();
            } else {
                console.log('Redco Debug: Page visible, restarting performance updates');
                // Only restart if we're still on the main dashboard
                const stillOnDashboard = window.location.href.includes('page=redco-optimizer') && !window.location.href.includes('&tab=');
                if (stillOnDashboard) {
                    startPerformanceUpdates();
                } else {
                    console.log('Redco Debug: No longer on dashboard, not restarting updates');
                }
            }
        });

        // Stop updates when navigating away from dashboard
        $(window).on('beforeunload', function() {
            console.log('Redco Debug: Page unloading, stopping performance updates');
            stopPerformanceUpdates();
        });

        // Manual refresh button
        $('.performance-refresh-btn').on('click', function() {
            console.log('Redco Debug: Manual refresh triggered');
            updateAllDashboardSections(true, false); // manual=true, isInitial=false
        });
    }

    /**
     * Initialize performance monitoring with API calls (called after page load)
     */
    function initPerformanceMonitoring() {
        // Only initialize on main dashboard page
        const isMainDashboard = window.location.href.includes('page=redco-optimizer') && !window.location.href.includes('&tab=');
        const hasPerformanceElements = $('.redco-performance-overview, .redco-performance-health-dashboard, .performance-metric, .health-metric').length > 0;

        // Only start auto-updates on main dashboard with performance elements
        if (!isMainDashboard || !hasPerformanceElements) {
            console.log('Redco Debug: ❌ Skipping performance monitoring - not main dashboard or no performance elements');
            return;
        }

        // Check if monitoring is enabled
        if (redcoAjax.settings && !redcoAjax.settings.monitoringEnabled) {
            console.log('Redco Debug: ❌ Performance monitoring disabled in settings');
            return;
        }

        console.log('Redco Debug: ✅ Starting performance monitoring with interval:', config.performanceUpdateInterval + 'ms (' + (config.performanceUpdateInterval/1000) + 's)');

        // Start monitoring
        startPerformanceUpdates();
    }

    /**
     * Add performance indicator
     */
    function addPerformanceIndicator() {
        const indicator = `
            <div class="performance-indicator">
                <span class="indicator-dot"></span>
                <span class="indicator-text">Live</span>
                <button class="performance-refresh-btn" title="Refresh Now">
                    <span class="dashicons dashicons-update"></span>
                </button>
            </div>
        `;

        $('.redco-performance-overview h2').append(indicator);
    }

    /**
     * Start performance updates
     */
    function startPerformanceUpdates() {
        if (performanceUpdateActive) {
            console.log('Redco Debug: Performance updates already active');
            return;
        }

        console.log('Redco Debug: Starting performance updates with interval:', config.performanceUpdateInterval + 'ms');
        performanceUpdateActive = true;

        // Initial update of all sections (without showing auto-update indicator)
        updateAllDashboardSections(false, true); // manual=false, isInitial=true

        // Set up recurring updates
        performanceUpdateTimer = setInterval(function() {
            console.log('Redco Debug: ⏰ Auto-updating all dashboard sections at scheduled interval');
            updateAllDashboardSections(false, false); // manual=false, isInitial=false
        }, config.performanceUpdateInterval);
    }

    /**
     * Stop performance updates
     */
    function stopPerformanceUpdates() {
        console.log('Redco Debug: Stopping all performance updates');
        performanceUpdateActive = false;

        if (performanceUpdateTimer) {
            clearInterval(performanceUpdateTimer);
            performanceUpdateTimer = null;
            console.log('Redco Debug: Main performance timer cleared');
        }

        // Clear any other potential timers
        clearAllRedcoTimers();
    }

    /**
     * Clear all Redco timers to prevent multiple running timers
     */
    function clearAllRedcoTimers() {
        // Clear any timers that might be stored in window object
        if (window.redcoTimers) {
            window.redcoTimers.forEach(function(timerId) {
                clearInterval(timerId);
                clearTimeout(timerId);
            });
            window.redcoTimers = [];
        }

        console.log('Redco Debug: All Redco timers cleared');
    }

    /**
     * Update performance monitoring interval from settings
     */
    function updatePerformanceInterval(newIntervalSeconds) {
        const newIntervalMs = newIntervalSeconds * 1000;
        console.log('🔧 Redco Debug: Updating performance interval to:', newIntervalMs + 'ms (' + newIntervalSeconds + 's)');

        // Update config
        config.performanceUpdateInterval = newIntervalMs;

        // Restart monitoring with new interval if currently active
        if (performanceUpdateActive) {
            console.log('🔧 Redco Debug: Restarting monitoring with new interval');
            stopPerformanceUpdates();
            setTimeout(function() {
                startPerformanceUpdates();
            }, 1000);
        }
    }

    /**
     * Update all dashboard sections
     */
    function updateAllDashboardSections(manual = false, isInitial = false) {
        if (!performanceUpdateActive && !manual) {
            console.log('Redco Debug: Performance updates not active, skipping');
            return;
        }

        const updateType = manual ? 'manual' : (isInitial ? 'initial' : 'scheduled');
        console.log('Redco Debug: Updating all dashboard sections, type:', updateType);

        // Only show auto-update indicator for scheduled updates (not initial or manual)
        const showAutoUpdateIndicator = !manual && !isInitial;

        // Show loading state for all sections
        showAllSectionsLoading(true, showAutoUpdateIndicator);

        // Update all sections in parallel
        Promise.all([
            updatePerformanceMetricsSection(),
            updateWebsitePerformanceScores(),
            updateCoreWebVitalsChart(),
            updateHealthMetrics()
        ]).then(function() {
            console.log('Redco Debug: All dashboard sections updated successfully');
            updateLastUpdated();
        }).catch(function(error) {
            console.error('Redco Debug: Error updating dashboard sections:', error);
            // Retry after delay
            setTimeout(function() {
                if (performanceUpdateActive) {
                    console.log('Redco Debug: Retrying dashboard update after error');
                    updateAllDashboardSections(false, false); // Retry as scheduled update
                }
            }, config.performanceRetryDelay);
        }).finally(function() {
            // Hide loading state after all updates complete
            showAllSectionsLoading(false, showAutoUpdateIndicator);
        });
    }

    /**
     * Update performance metrics section only
     */
    function updatePerformanceMetrics(manual = false) {
        if (!performanceUpdateActive && !manual) {
            console.log('Redco Debug: Performance updates not active, skipping');
            return;
        }

        console.log('Redco Debug: Updating performance metrics, manual:', manual);

        // Show loading state
        $('.performance-indicator .indicator-dot').addClass('loading');

        return updatePerformanceMetricsSection().then(function() {
            updateLastUpdated();
            console.log('Redco Debug: Performance metrics updated successfully');
        }).catch(function(error) {
            console.error('Redco Debug: Performance update error:', error);
            // Retry after delay
            setTimeout(function() {
                if (performanceUpdateActive) {
                    console.log('Redco Debug: Retrying performance update after error');
                    updatePerformanceMetrics();
                }
            }, config.performanceRetryDelay);
        }).finally(function() {
            $('.performance-indicator .indicator-dot').removeClass('loading');
        });
    }

    /**
     * Update performance metrics section via AJAX
     */
    function updatePerformanceMetricsSection() {
        return new Promise(function(resolve, reject) {
            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_get_performance_metrics',
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    console.log('Redco Debug: Performance metrics response:', response);
                    if (response.success) {
                        updatePerformanceDisplay(response.data);
                        resolve(response.data);
                    } else {
                        console.warn('Redco Debug: Performance update failed:', response.data);
                        reject(response.data);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Redco Debug: Performance AJAX error:', status, error);
                    console.error('Redco Debug: Response text:', xhr.responseText);

                    // Show user-friendly error message
                    if (status === 'error' && xhr.status === 500) {
                        console.error('Redco Debug: Server error (500) - check PHP error logs');
                    }

                    reject(error);
                }
            });
        });
    }

    /**
     * Show/hide loading states for all dashboard sections
     */
    function showAllSectionsLoading(isLoading, showAutoIndicator = false) {
        const updateType = showAutoIndicator ? 'scheduled auto-update' : 'manual/initial update';
        console.log('Redco Debug: Setting loading state for all sections:', isLoading, 'type:', updateType, 'at time:', new Date().toLocaleTimeString());

        // Performance Metrics section - keep existing dot animation
        $('.performance-indicator .indicator-dot').toggleClass('loading', isLoading);

        if (isLoading) {
            if (showAutoIndicator) {
                console.log('Redco Debug: ⏰ Triggering scheduled auto-update animations at interval:', config.performanceUpdateInterval + 'ms');
            } else {
                console.log('Redco Debug: 🔄 Triggering manual/initial update animations');
            }

            // Website Performance Scores - trigger spin animation only when data is actually updating
            $('.score-item .score-circle .score-number').each(function() {
                const $element = $(this);
                // Only animate if not already animating
                if (!$element.hasClass('animating')) {
                    triggerScoreAnimation($element);
                }
            });

            // Core Web Vitals Chart - add loading class to chart container
            $('.core-web-vitals-chart, [id*="coreWebVitalsChart"]').addClass('chart-loading');

            // Health Metrics - trigger spin animation for metric values
            $('.health-metric .metric-value, .metric-card .metric-value').each(function() {
                const $element = $(this);
                if (!$element.hasClass('animating')) {
                    triggerScoreAnimation($element);
                }
            });

            // Generic metric cards - trigger spin for any score numbers
            $('.score-number, .metric-number, .stat-number').not('.animating').each(function() {
                triggerScoreAnimation($(this));
            });

            // Only show auto-update indicator for scheduled updates
            if (showAutoIndicator) {
                showAutoUpdateIndicator();
            }

        } else {
            // Remove loading states when update completes
            $('.core-web-vitals-chart, [id*="coreWebVitalsChart"]').removeClass('chart-loading');

            // Only hide auto-update indicator if it was shown
            if (showAutoIndicator) {
                hideAutoUpdateIndicator();
            }
        }

        console.log('Redco Debug: Animations completed for', updateType);
    }

    /**
     * Show auto-update indicator
     */
    function showAutoUpdateIndicator() {
        // Remove existing indicator
        $('.redco-auto-update-indicator').remove();

        const nextUpdate = Math.round(config.performanceUpdateInterval / 1000);
        const $indicator = $(`
            <div class="redco-auto-update-indicator" style="
                position: fixed;
                top: 32px;
                right: 20px;
                background: #4CAF50;
                color: white;
                padding: 8px 12px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: bold;
                z-index: 999999;
                box-shadow: 0 2px 8px rgba(0,0,0,0.3);
                animation: redco-pulse 1.5s ease-in-out infinite;
            ">
                🔄 Auto-updating... (${nextUpdate}s interval)
            </div>
        `);

        $('body').append($indicator);
    }

    /**
     * Hide auto-update indicator
     */
    function hideAutoUpdateIndicator() {
        $('.redco-auto-update-indicator').fadeOut(300, function() {
            $(this).remove();
        });
    }

    /**
     * Trigger score card spin animation (same as page load)
     */
    function triggerScoreAnimation($element) {
        if ($element.hasClass('animating')) {
            return; // Already animating
        }

        $element.addClass('animating');

        // Store original value
        const originalValue = $element.text();
        const targetValue = parseInt(originalValue) || 0;

        // Start spinning animation
        let currentValue = 0;
        const increment = Math.max(1, Math.floor(targetValue / 20));
        const duration = 1000; // 1 second
        const steps = 20;
        const stepDuration = duration / steps;

        const animationInterval = setInterval(function() {
            currentValue += increment;
            if (currentValue >= targetValue) {
                currentValue = targetValue;
                $element.text(currentValue);
                clearInterval(animationInterval);
                $element.removeClass('animating');
            } else {
                $element.text(currentValue);
            }
        }, stepDuration);
    }

    /**
     * Animate number to target value (for score updates)
     */
    function animateNumberToTarget($element, targetValue) {
        if ($element.hasClass('animating')) {
            return; // Already animating
        }

        $element.addClass('animating');

        const startValue = parseInt($element.text()) || 0;
        const target = parseInt(targetValue) || 0;

        // If values are the same, no animation needed
        if (startValue === target) {
            $element.removeClass('animating');
            return;
        }

        const duration = 1200; // 1.2 seconds
        const steps = 30;
        const stepDuration = duration / steps;
        const increment = (target - startValue) / steps;

        let currentValue = startValue;
        let step = 0;

        const animationInterval = setInterval(function() {
            step++;
            currentValue += increment;

            if (step >= steps) {
                currentValue = target;
                $element.text(Math.round(currentValue));
                clearInterval(animationInterval);
                $element.removeClass('animating');
            } else {
                $element.text(Math.round(currentValue));
            }
        }, stepDuration);
    }

    /**
     * Update Website Performance Scores section
     */
    function updateWebsitePerformanceScores() {
        return new Promise(function(resolve, reject) {
            console.log('Redco Debug: Updating Website Performance Scores');

            // Disable device toggle during auto-refresh
            disableDeviceToggle();

            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_get_pagespeed_scores',
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    console.log('Redco Debug: PageSpeed scores response:', response);
                    if (response.success) {
                        updatePageSpeedDisplay(response.data);
                        resolve(response.data);
                    } else {
                        console.warn('Redco Debug: PageSpeed scores update failed:', response.data);
                        reject(response.data);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Redco Debug: PageSpeed scores AJAX error:', status, error);
                    console.error('Redco Debug: Response text:', xhr.responseText);

                    // Show user-friendly error message
                    if (status === 'error' && xhr.status === 500) {
                        console.error('Redco Debug: Server error (500) - check PHP error logs');
                    }

                    reject(error);
                },
                complete: function() {
                    // Re-enable device toggle after auto-refresh completes (success or error)
                    enableDeviceToggle();
                }
            });
        });
    }

    /**
     * Update PageSpeed display with spin animations
     */
    function updatePageSpeedDisplay(data) {
        // Update Performance Score - using correct selector for HTML structure
        const $performanceScore = $('.performance-score-item .score-circle');
        if ($performanceScore.length) {
            const $scoreNumber = $performanceScore.find('.score-number');

            // Update score with animation
            animateNumberToTarget($scoreNumber, data.performance_score);

            // Update data attribute for circle animation
            $performanceScore.attr('data-score', data.performance_score);
            $performanceScore.css('--score-percentage', data.performance_score);
        }

        // Update Accessibility Score
        const $accessibilityScore = $('.accessibility-score-item .score-circle');
        if ($accessibilityScore.length) {
            const $scoreNumber = $accessibilityScore.find('.score-number');

            animateNumberToTarget($scoreNumber, data.accessibility_score);
            $accessibilityScore.attr('data-score', data.accessibility_score);
            $accessibilityScore.css('--score-percentage', data.accessibility_score);
        }

        // Update Best Practices Score
        const $bestPracticesScore = $('.best-practices-score-item .score-circle');
        if ($bestPracticesScore.length) {
            const $scoreNumber = $bestPracticesScore.find('.score-number');

            animateNumberToTarget($scoreNumber, data.best_practices_score);
            $bestPracticesScore.attr('data-score', data.best_practices_score);
            $bestPracticesScore.css('--score-percentage', data.best_practices_score);
        }

        // Update SEO Score
        const $seoScore = $('.seo-score-item .score-circle');
        if ($seoScore.length) {
            const $scoreNumber = $seoScore.find('.score-number');

            animateNumberToTarget($scoreNumber, data.seo_score);
            $seoScore.attr('data-score', data.seo_score);
            $seoScore.css('--score-percentage', data.seo_score);
        }
    }

    /**
     * Update Core Web Vitals Chart
     */
    function updateCoreWebVitalsChart() {
        return new Promise(function(resolve, reject) {
            console.log('Redco Debug: Updating Core Web Vitals Chart');

            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_get_core_web_vitals_data',
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    console.log('Redco Debug: Core Web Vitals response:', response);
                    if (response.success) {
                        updateCoreWebVitalsChartDisplay(response.data);
                        resolve(response.data);
                    } else {
                        console.warn('Redco Debug: Core Web Vitals update failed:', response.data);
                        reject(response.data);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Redco Debug: Core Web Vitals AJAX error:', status, error);
                    console.error('Redco Debug: Response text:', xhr.responseText);

                    // Show user-friendly error message
                    if (status === 'error' && xhr.status === 500) {
                        console.error('Redco Debug: Server error (500) - check PHP error logs');
                    }

                    reject(error);
                }
            });
        });
    }

    /**
     * Update Core Web Vitals Chart Display
     */
    function updateCoreWebVitalsChartDisplay(data) {
        console.log('Redco Debug: Updating Core Web Vitals chart display with data:', data);

        // Validate data first
        if (!data || !Array.isArray(data) || data.length === 0) {
            console.warn('Redco Debug: Invalid or empty Core Web Vitals data received');
            return;
        }

        // Check if Chart.js and chart instance exist
        if (typeof Chart === 'undefined') {
            console.warn('Redco Debug: Chart.js not loaded, cannot update chart');
            return;
        }

        if (!window.coreWebVitalsChart) {
            console.log('Redco Debug: Chart instance not available, reinitializing...');
            setTimeout(function() {
                initCoreWebVitalsChart();
                // Try to update again after initialization
                setTimeout(function() {
                    if (window.coreWebVitalsChart) {
                        updateCoreWebVitalsChartDisplay(data);
                    }
                }, 1000);
            }, 500);
            return;
        }

        try {
            // Update chart data
            const chart = window.coreWebVitalsChart;

            // Validate chart object
            if (!chart || !chart.data || !chart.data.datasets || typeof chart.update !== 'function') {
                console.error('Redco Debug: Chart object is invalid or missing data structure');
                console.error('Redco Debug: Chart object details:', {
                    exists: !!chart,
                    hasData: !!(chart && chart.data),
                    hasDatasets: !!(chart && chart.data && chart.data.datasets),
                    hasUpdateMethod: !!(chart && typeof chart.update === 'function'),
                    chartType: chart ? typeof chart : 'undefined'
                });

                // Try to reinitialize
                window.coreWebVitalsChart = null;
                setTimeout(function() {
                    initCoreWebVitalsChart();
                }, 1000);
                return;
            }

            // Prepare new data
            const labels = data.map(item => {
                const date = new Date(item.date);
                return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
            });

            const lcpData = data.map(item => parseFloat(item.lcp) || 0);
            const fidData = data.map(item => parseInt(item.fid) || 0);
            const clsData = data.map(item => (parseFloat(item.cls) || 0) * 1000); // Convert to milliseconds for better visualization

            console.log('Redco Debug: Prepared chart data:', { labels, lcpData, fidData, clsData });

            // Ensure we have the right number of datasets
            if (chart.data.datasets.length < 3) {
                console.error('Redco Debug: Chart does not have enough datasets');
                return;
            }

            // Update chart
            chart.data.labels = labels;
            chart.data.datasets[0].data = lcpData;
            chart.data.datasets[1].data = fidData;
            chart.data.datasets[2].data = clsData;

            // Animate the update
            chart.update('active');

            console.log('Redco Debug: Core Web Vitals chart updated successfully');
        } catch (error) {
            console.error('Redco Debug: Error updating Core Web Vitals chart:', error);
            console.error('Redco Debug: Chart object state:', window.coreWebVitalsChart);

            // Try to recover by reinitializing
            window.coreWebVitalsChart = null;
            setTimeout(function() {
                initCoreWebVitalsChart();
            }, 1000);
        }
    }

    /**
     * Update performance display
     */
    function updatePerformanceDisplay(data) {
        // Update performance score
        updateMetricCard('.performance-score', data.score, data.score_text, data.score_class);

        // Update load time
        updateMetricCard('[data-metric="load_time"]', data.load_time + 's', data.load_time_text, data.load_time_trend);

        // Update database queries
        updateMetricCard('[data-metric="db_queries"]', data.db_queries, data.db_text, data.db_trend);

        // Update memory usage
        updateMetricCard('[data-metric="memory_usage"]', data.memory_usage + 'MB', data.memory_text, data.memory_trend);

        // Update file size
        updateMetricCard('[data-metric="file_size"]', data.total_file_size + 'KB', data.file_size_text, data.file_size_trend);

        // Update HTTP requests
        updateMetricCard('[data-metric="http_requests"]', data.http_requests, data.http_text, data.http_trend);

        // Update stats cards
        $('.modules-stat .stat-number').text(data.active_modules + '/' + data.total_modules);
        $('.cache-stat .stat-number').text(data.cache_status);
        $('.optimization-stat .stat-number').text(data.optimizations);

        // Update header module count for consistency across all tabs (header, sidebar, and compact)
        $('#header-module-count, #sidebar-module-count').text(data.active_modules + '/' + data.total_modules);
    }

    /**
     * Update individual metric card
     */
    function updateMetricCard(selector, value, text, trend) {
        const $card = $(selector);
        if (!$card.length) return;

        // Update value with animation
        const $number = $card.find('.score-number, .metric-number');
        const $status = $card.find('.performance-status, .performance-trend');

        // Animate value change
        $number.fadeOut(150, function() {
            $(this).text(value).fadeIn(150);
        });

        // Update status
        $status.removeClass('excellent good average poor')
               .addClass(trend)
               .text(text);
    }

    /**
     * Update last updated timestamp
     */
    function updateLastUpdated() {
        const now = new Date();
        const timeString = now.toLocaleTimeString();

        let $timestamp = $('.performance-timestamp');
        if (!$timestamp.length) {
            $timestamp = $('<div class="performance-timestamp">Last updated: ' + timeString + '</div>');
            $('.redco-performance-overview').append($timestamp);
        } else {
            $timestamp.text('Last updated: ' + timeString);
        }
    }

    /**
     * Initialize keyboard shortcuts
     */
    function initKeyboardShortcuts() {
        $(document).on('keydown', function(e) {
            // Ctrl/Cmd + S to save current form
            if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                e.preventDefault();
                const $form = $('.redco-module-form:visible');
                if ($form.length) {
                    $form.submit();
                    showToast('Settings saved!', 'success');
                }
            }

            // Escape to close toasts
            if (e.key === 'Escape') {
                $('.redco-toast').removeClass('show');
            }

            // Ctrl/Cmd + R to refresh performance metrics
            if ((e.ctrlKey || e.metaKey) && e.key === 'r' && $('.redco-performance-overview').length) {
                e.preventDefault();
                updatePerformanceMetrics(true);
                showToast('Performance metrics refreshed', 'info');
            }
        });
    }

    /**
     * Utility function to get URL parameter
     */
    function getUrlParameter(name) {
        name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
        const regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
        const results = regex.exec(location.search);
        return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
    }

    /**
     * Initialize enhanced exclude sections
     */
    function initExcludeSections() {
        // Convert existing exclude sections to enhanced format
        $('.redco-form-section').each(function() {
            const $section = $(this);
            const $checkboxList = $section.find('.checkbox-list');

            if ($checkboxList.length && $checkboxList.find('.checkbox-item').length > 10) {
                enhanceExcludeSection($section, $checkboxList);
            }
        });
    }

    /**
     * Enhance exclude section with collapsible, searchable interface
     */
    function enhanceExcludeSection($section, $checkboxList) {
        const sectionTitle = $section.find('h3, h4').first().text() || 'Exclude Files';
        const totalItems = $checkboxList.find('.checkbox-item').length;
        const checkedItems = $checkboxList.find('input:checked').length;

        // Create enhanced structure
        const enhancedHTML = `
            <div class="redco-exclude-section">
                <div class="redco-exclude-header">
                    <h4>
                        <span class="dashicons dashicons-list-view"></span>
                        ${sectionTitle}
                    </h4>
                    <div class="redco-exclude-stats">
                        <span class="selected-count">${checkedItems}</span>/<span class="total-count">${totalItems}</span> selected
                        <button type="button" class="redco-exclude-toggle">▼</button>
                    </div>
                </div>
                <div class="redco-exclude-content">
                    <div class="redco-exclude-search">
                        <input type="text" placeholder="Search files..." class="exclude-search-input">
                    </div>
                    <div class="redco-exclude-list">
                        <div class="checkbox-list"></div>
                    </div>
                    <div class="redco-bulk-actions">
                        <button type="button" class="select-all-btn">Select All</button>
                        <button type="button" class="select-none-btn">Select None</button>
                        <button type="button" class="select-common-btn">Select Common</button>
                    </div>
                </div>
            </div>
        `;

        // Replace the original checkbox list
        const $enhanced = $(enhancedHTML);
        $enhanced.find('.checkbox-list').html($checkboxList.html());
        $checkboxList.closest('.redco-form-row').replaceWith($enhanced);

        // Initialize functionality
        initExcludeSectionEvents($enhanced);
        initLazyLoading($enhanced);
    }

    /**
     * Initialize exclude section events
     */
    function initExcludeSectionEvents($section) {
        const $header = $section.find('.redco-exclude-header');
        const $content = $section.find('.redco-exclude-content');
        const $toggle = $section.find('.redco-exclude-toggle');
        const $search = $section.find('.exclude-search-input');
        const $checkboxes = $section.find('input[type="checkbox"]');

        // Toggle expand/collapse
        $header.on('click', function() {
            const isExpanded = $content.hasClass('expanded');

            if (isExpanded) {
                $content.removeClass('expanded');
                $toggle.text('▼');
            } else {
                $content.addClass('expanded');
                $toggle.text('▲');

                // Lazy load visible items
                lazyLoadVisibleItems($section);
            }
        });

        // Search functionality
        const debouncedSearch = debounce(function() {
            const searchTerm = $search.val().toLowerCase();
            filterCheckboxItems($section, searchTerm);
        }, 300);

        $search.on('input', debouncedSearch);

        // Bulk actions
        $section.find('.select-all-btn').on('click', function() {
            $checkboxes.prop('checked', true);
            updateStats($section);
        });

        $section.find('.select-none-btn').on('click', function() {
            $checkboxes.prop('checked', false);
            updateStats($section);
        });

        $section.find('.select-common-btn').on('click', function() {
            // Select commonly excluded items
            $checkboxes.each(function() {
                const $checkbox = $(this);
                const label = $checkbox.next('label').text().toLowerCase();

                // Common exclusions
                const commonPatterns = ['jquery', 'bootstrap', 'fontawesome', 'admin', 'login', 'customize'];
                const shouldSelect = commonPatterns.some(pattern => label.includes(pattern));

                $checkbox.prop('checked', shouldSelect);
            });
            updateStats($section);
        });

        // Update stats on checkbox change
        $checkboxes.on('change', function() {
            updateStats($section);
        });

        // Initial stats
        updateStats($section);
    }

    /**
     * Filter checkbox items based on search term
     */
    function filterCheckboxItems($section, searchTerm) {
        const $items = $section.find('.checkbox-item');
        let visibleCount = 0;

        $items.each(function() {
            const $item = $(this);
            const text = $item.find('label').text().toLowerCase();
            const matches = text.includes(searchTerm);

            $item.toggle(matches);
            if (matches) visibleCount++;
        });

        // Show "no results" message if needed
        const $list = $section.find('.redco-exclude-list');
        $list.find('.no-results').remove();

        if (visibleCount === 0 && searchTerm) {
            $list.append('<div class="no-results" style="text-align: center; padding: 20px; color: #666;">No files found matching "' + searchTerm + '"</div>');
        }
    }

    /**
     * Update statistics display
     */
    function updateStats($section) {
        const $checkboxes = $section.find('input[type="checkbox"]');
        const total = $checkboxes.length;
        const checked = $checkboxes.filter(':checked').length;

        $section.find('.selected-count').text(checked);
        $section.find('.total-count').text(total);
    }

    /**
     * Initialize lazy loading for checkbox items
     */
    function initLazyLoading($section) {
        const $items = $section.find('.checkbox-item');

        if ($items.length > 50) {
            // Hide items beyond the first 50
            $items.slice(50).hide().addClass('lazy-hidden');

            // Add "Load More" button
            const $loadMore = $('<button type="button" class="load-more-btn" style="width: 100%; padding: 12px; margin-top: 16px; border: 1px dashed #ccc; background: none; cursor: pointer;">Load More (' + ($items.length - 50) + ' remaining)</button>');

            $section.find('.redco-exclude-list').append($loadMore);

            $loadMore.on('click', function() {
                const $hiddenItems = $section.find('.checkbox-item.lazy-hidden');
                const $nextBatch = $hiddenItems.slice(0, 50);

                $nextBatch.removeClass('lazy-hidden').fadeIn(200);

                const remaining = $hiddenItems.length - 50;
                if (remaining <= 0) {
                    $loadMore.remove();
                } else {
                    $loadMore.text('Load More (' + remaining + ' remaining)');
                }
            });
        }
    }

    /**
     * Lazy load visible items when section is expanded
     */
    function lazyLoadVisibleItems($section) {
        const $items = $section.find('.checkbox-item');

        // Add loading animation to items that aren't visible yet
        $items.slice(20).each(function(index) {
            const $item = $(this);

            setTimeout(() => {
                $item.addClass('animate-in');
            }, index * 50);
        });
    }

    /**
     * Debounce function for performance
     */
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * Initialize Performance & Health Dashboard UI only (no API calls)
     */
    function initHealthMonitorUI() {
        // Only initialize on dashboard page
        if (!$('.redco-performance-health-dashboard').length) {
            return;
        }

        console.log('Redco Debug: ✅ Initializing health monitor UI (API calls deferred)');

        // Initialize health score ring animation
        initHealthScoreRing();

        // Initialize PageSpeed score circles
        initPageSpeedScores();

        // Initialize live metrics updates
        initLiveMetrics();

        // Initialize refresh all button
        initRefreshAllButton();

        // Initialize optimization opportunities
        initOptimizationOpportunities();

        // Initialize quick actions
        initHealthQuickActions();
    }

    /**
     * Initialize Performance & Health Dashboard with API calls (called after page load)
     */
    function initHealthMonitor() {
        // Only initialize on dashboard page
        if (!$('.redco-performance-health-dashboard').length) {
            return;
        }

        console.log('Redco Debug: ✅ Starting health monitor with API calls');

        // Start auto-refresh
        startHealthMonitorUpdates();
    }

    /**
     * Initialize deferred API updates after page load completion
     * This function is called after the loading screen is hidden
     */
    function initDeferredAPIUpdates() {
        console.log('🚀 Redco Debug: Starting deferred API updates after page load completion');

        // Only run on dashboard page
        const isMainDashboard = window.location.href.includes('page=redco-optimizer') && !window.location.href.includes('&tab=');
        if (!isMainDashboard) {
            console.log('Redco Debug: ❌ Not on main dashboard, skipping deferred API updates');
            return;
        }

        // Check if monitoring is enabled
        if (redcoAjax.settings && !redcoAjax.settings.monitoringEnabled) {
            console.log('Redco Debug: ❌ Performance monitoring disabled in settings, skipping API updates');
            return;
        }

        // Add a small delay to ensure page is fully settled
        setTimeout(() => {
            console.log('🔄 Redco Debug: Initiating Google PageSpeed Insights API calls...');

            // Start performance monitoring (which includes API calls)
            initPerformanceMonitoring();

            // Start health monitoring (which includes API calls)
            initHealthMonitor();

            console.log('✅ Redco Debug: Deferred API updates initiated successfully');
        }, 1000); // 1 second delay after page load completion
    }

    /**
     * Initialize health score ring animation
     */
    function initHealthScoreRing() {
        const $ring = $('.health-score-ring');
        if (!$ring.length) return;

        const score = parseInt($ring.data('score')) || 0;
        const angle = (score / 100) * 360;

        // Animate the ring
        $ring.css('--score-angle', angle + 'deg');

        // Animate the number
        animateNumber($ring.find('.health-score-number'), 0, score, 1500);
    }

    /**
     * Initialize PageSpeed score circles
     */
    function initPageSpeedScores() {
        $('.score-circle').each(function() {
            const $circle = $(this);
            const score = parseInt($circle.attr('data-score'));

            // Set CSS custom property for percentage
            $circle.css('--score-percentage', score);

            // Animate the score number
            const $scoreNumber = $circle.find('.score-number');
            animateNumber($scoreNumber, 0, score, 1500);

            // Add animation delay for staggered effect
            const delay = $('.score-circle').index(this) * 200;
            setTimeout(() => {
                $circle.addClass('animated');
            }, delay);
        });
    }

    /**
     * Initialize live metrics updates
     */
    function initLiveMetrics() {
        // Refresh metrics button
        $('.refresh-metrics-btn').on('click', function() {
            const $btn = $(this);
            $btn.addClass('loading');

            updateHealthMetrics().finally(() => {
                $btn.removeClass('loading');
            });
        });

        // Add hover effects to metric items
        $('.metric-item, .performance-card').on('mouseenter', function() {
            $(this).addClass('hover');
        }).on('mouseleave', function() {
            $(this).removeClass('hover');
        });
    }

    /**
     * Initialize refresh all button
     */
    function initRefreshAllButton() {
        $('.refresh-all-btn').on('click', function() {
            const $btn = $(this);
            const $icon = $btn.find('.dashicons');

            $btn.prop('disabled', true);
            $icon.addClass('fa-spin');

            // Show loading state
            showToast('Refreshing all metrics...', 'info', 2000);

            // Refresh all data
            Promise.all([
                updateHealthMetrics(),
                updatePerformanceMetrics(),
                updateHealthScore()
            ]).then(() => {
                showToast('All metrics refreshed successfully!', 'success');
            }).catch(() => {
                showToast('Error refreshing metrics', 'error');
            }).finally(() => {
                $btn.prop('disabled', false);
                $icon.removeClass('fa-spin');
            });
        });
    }

    /**
     * Update performance metrics
     */
    function updatePerformanceMetrics() {
        return new Promise((resolve, reject) => {
            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_get_performance_metrics',
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        updatePerformanceDisplay(response.data);
                        resolve(response.data);
                    } else {
                        reject(response.data);
                    }
                },
                error: function() {
                    reject('Network error');
                }
            });
        });
    }

    /**
     * Update performance display
     */
    function updatePerformanceDisplay(data) {
        // Update performance cards with new data
        $('.performance-card[data-metric="load_time"] .metric-number').text(data.load_time);
        $('.performance-card[data-metric="db_queries"] .metric-number').text(data.db_queries);
        $('.performance-card[data-metric="memory_usage"] .metric-number').text(data.memory_usage);
        $('.performance-card[data-metric="file_size"] .metric-number').text(data.total_file_size);
        $('.performance-card[data-metric="http_requests"] .metric-number').text(data.http_requests);

        // Update performance score
        $('.performance-score .score-number').text(data.score);
        $('.performance-score .performance-status')
            .removeClass('excellent good average poor')
            .addClass(data.score_class)
            .text(data.score_text);

        // Add update animation
        $('.performance-card').addClass('updated');
        setTimeout(() => $('.performance-card').removeClass('updated'), 500);
    }

    /**
     * Initialize optimization opportunities
     */
    function initOptimizationOpportunities() {
        // Enable module buttons
        $('.enable-module-btn').on('click', function() {
            const $btn = $(this);
            const module = $btn.data('module');

            $btn.prop('disabled', true).html('<span class="dashicons dashicons-update"></span>');

            // Simulate module toggle
            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_toggle_module',
                    module: module,
                    enabled: true,
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        showToast('Module enabled successfully!', 'success');

                        // Remove the opportunity item
                        $btn.closest('.opportunity-item').fadeOut(300, function() {
                            $(this).remove();
                            updateOpportunitiesCount();
                        });

                        // Update health score
                        setTimeout(updateHealthScore, 500);
                    } else {
                        showToast(response.data.message || 'Failed to enable module', 'error');
                    }
                },
                error: function() {
                    showToast('Error enabling module', 'error');
                },
                complete: function() {
                    $btn.prop('disabled', false).html('<span class="dashicons dashicons-yes"></span>');
                }
            });
        });
    }

    /**
     * Initialize health monitor quick actions (legacy - now using standardized system)
     */
    function initHealthQuickActions() {
        // Note: Quick action buttons now use standardized progress modal system
        // Only handle non-standardized actions here

        $('.quick-action-btn[data-action="run_health_check"]').on('click', function() {
            const $btn = $(this);
            $btn.addClass('loading').prop('disabled', true);
            runHealthCheck($btn);
        });
    }

    /**
     * Perform quick action
     */
    function performQuickAction(ajaxAction, successMessage, $btn) {
        $.ajax({
            url: redcoAjax.ajaxurl,
            type: 'POST',
            data: {
                action: ajaxAction,
                nonce: redcoAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    showToast(successMessage, 'success');

                    // Update metrics after action
                    setTimeout(updateHealthMetrics, 1000);
                } else {
                    showToast(response.data.message || 'Action failed', 'error');
                }
            },
            error: function() {
                showToast('Error performing action', 'error');
            },
            complete: function() {
                $btn.removeClass('loading').prop('disabled', false);
            }
        });
    }

    /**
     * Run comprehensive health check
     */
    function runHealthCheck($btn) {
        showToast('Running comprehensive health check...', 'info');

        // Simulate health check process
        let progress = 0;
        const interval = setInterval(() => {
            progress += 20;

            if (progress <= 100) {
                showToast(`Health check progress: ${progress}%`, 'info', 1000);
            }

            if (progress >= 100) {
                clearInterval(interval);
                showToast('Health check completed! All systems optimal.', 'success');
                updateHealthScore();
                $btn.removeClass('loading').prop('disabled', false);
            }
        }, 800);
    }

    /**
     * Start health monitor auto-updates
     */
    function startHealthMonitorUpdates() {
        console.log('Redco Debug: Health monitor updates are now handled by main performance timer');
        console.log('Redco Debug: Health metrics will update with main dashboard at interval:', config.performanceUpdateInterval + 'ms');

        // Health metrics are now updated as part of updateAllDashboardSections()
        // No separate timer needed - this prevents multiple timers running

        // Note: Initial health metrics update is now handled by the main performance monitoring
        // to ensure all API calls happen together after page load completion
        console.log('Redco Debug: Health metrics will be updated by main performance monitoring system');
    }

    /**
     * Update health metrics
     */
    function updateHealthMetrics() {
        return new Promise((resolve, reject) => {
            console.log('Redco Debug: Fetching health metrics via AJAX');

            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_get_health_metrics',
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    console.log('Redco Debug: Health metrics response:', response);
                    if (response.success) {
                        updateHealthDisplay(response.data);
                        resolve(response.data);
                        console.log('Redco Debug: Health metrics updated successfully');
                    } else {
                        console.warn('Redco Debug: Health metrics update failed:', response.data);
                        reject(response.data);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Redco Debug: Health metrics AJAX error:', status, error);
                    console.error('Redco Debug: Response text:', xhr.responseText);

                    // Show user-friendly error message
                    if (status === 'error' && xhr.status === 500) {
                        console.error('Redco Debug: Server error (500) - check PHP error logs');
                    }

                    reject('Network error');
                }
            });
        });
    }

    /**
     * Update health display
     */
    function updateHealthDisplay(data) {
        // Update live metrics
        $('#live-page-speed').text(data.page_speed + 's');
        $('#live-cache-hits').text(data.cache_hit_rate + '%');
        $('#live-optimizations').text(data.active_optimizations);
        $('#live-savings').text(data.bandwidth_savings + 'KB');

        // Add subtle animation to updated values
        $('.metric-value').addClass('updated');
        setTimeout(() => $('.metric-value').removeClass('updated'), 500);
    }

    /**
     * Update health score
     */
    function updateHealthScore() {
        $.ajax({
            url: redcoAjax.ajaxurl,
            type: 'POST',
            data: {
                action: 'redco_calculate_health_score',
                nonce: redcoAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    const newScore = response.data.score;
                    const $scoreNumber = $('.health-score-number');
                    const $scoreRing = $('.health-score-ring');
                    const $scoreStatus = $('.health-score-status');

                    // Animate score change
                    animateNumber($scoreNumber, parseInt($scoreNumber.text()), newScore, 1000);

                    // Update ring
                    const angle = (newScore / 100) * 360;
                    $scoreRing.css('--score-angle', angle + 'deg');

                    // Update status
                    $scoreStatus.removeClass('excellent good fair poor')
                              .addClass(response.data.status_class)
                              .text(response.data.status_text);
                }
            }
        });
    }

    /**
     * Update opportunities count
     */
    function updateOpportunitiesCount() {
        const count = $('.opportunity-item').length;
        $('.opportunities-count').text(count);

        if (count === 0) {
            $('.opportunities-list').html(`
                <div class="no-opportunities" style="text-align: center; padding: 40px; color: #666;">
                    <span class="dashicons dashicons-yes-alt" style="font-size: 48px; color: #4CAF50; margin-bottom: 16px;"></span>
                    <h4>All Optimizations Enabled!</h4>
                    <p>Your website is fully optimized. Great job!</p>
                </div>
            `);
        }
    }

    /**
     * Animate number changes
     */
    function animateNumber($element, start, end, duration) {
        const startTime = performance.now();

        function updateNumber(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // Easing function
            const easeOut = 1 - Math.pow(1 - progress, 3);
            const current = Math.round(start + (end - start) * easeOut);

            $element.text(current);

            if (progress < 1) {
                requestAnimationFrame(updateNumber);
            }
        }

        requestAnimationFrame(updateNumber);
    }

    /**
     * Initialize Core Web Vitals Chart
     */
    function initCoreWebVitalsChart() {
        console.log('Initializing Core Web Vitals Chart...');

        const chartCanvas = document.getElementById('coreWebVitalsChart');
        if (!chartCanvas) {
            console.log('Chart canvas not found - element with ID "coreWebVitalsChart" does not exist');
            return;
        }

        console.log('Chart canvas found:', chartCanvas);

        // Check if Chart.js is loaded
        if (typeof Chart === 'undefined') {
            console.error('Chart.js is not loaded - trying simple fallback');
            // Show fallback message
            chartCanvas.parentElement.innerHTML = '<div style="text-align: center; padding: 40px; color: #666;"><p>Loading chart library...</p></div>';
            // Try to load it again
            setTimeout(initCoreWebVitalsChart, 2000);
            return;
        }

        // Verify Chart.js is properly initialized
        if (!Chart.register || !Chart.Chart) {
            console.error('Chart.js is loaded but not properly initialized');
            chartCanvas.parentElement.innerHTML = '<div style="text-align: center; padding: 40px; color: #666;"><p>Chart library initialization error. Please refresh the page.</p></div>';
            return;
        }

        // Destroy existing chart if it exists
        if (window.coreWebVitalsChart) {
            console.log('Destroying existing chart instance...');
            try {
                // Check if it's a valid Chart.js instance
                if (typeof window.coreWebVitalsChart.destroy === 'function') {
                    window.coreWebVitalsChart.destroy();
                    console.log('Chart destroyed successfully');
                } else {
                    console.warn('Chart object does not have destroy method, clearing reference');
                }
            } catch (e) {
                console.warn('Error destroying existing chart:', e);
            }
            window.coreWebVitalsChart = null;
        }

        // Also clear any existing canvas context
        const ctx = chartCanvas.getContext('2d');
        if (ctx) {
            ctx.clearRect(0, 0, chartCanvas.width, chartCanvas.height);
        }

        console.log('Chart.js is loaded, version:', Chart.version || 'unknown');
        console.log('Chart.js available methods:', Object.keys(Chart));

        // First, try a simple test chart
        try {
            const testChart = new Chart(chartCanvas, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Feb', 'Mar'],
                    datasets: [{
                        label: 'Test',
                        data: [1, 2, 3],
                        borderColor: '#4CAF50'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
            console.log('Test chart created successfully:', testChart);

            // Destroy test chart and create real one
            testChart.destroy();
        } catch (testError) {
            console.error('Test chart failed:', testError);
            chartCanvas.parentElement.innerHTML = '<div style="text-align: center; padding: 40px; color: #666;"><p>Chart.js test failed: ' + testError.message + '</p></div>';
            return;
        }

        // Get data from the JSON script tag
        const dataScript = document.getElementById('coreWebVitalsData');
        if (!dataScript) {
            console.log('Chart data script not found');
            return;
        }

        try {
            const vitalsData = JSON.parse(dataScript.textContent);
            console.log('Chart data loaded:', vitalsData);

            // Validate data and provide fallback
            if (!Array.isArray(vitalsData) || vitalsData.length === 0) {
                console.warn('Invalid or empty Core Web Vitals data, using fallback data');
                vitalsData = [
                    { date: new Date().toISOString().split('T')[0], lcp: 2.5, fid: 100, cls: 0.1 }
                ];
            }

            // Prepare chart data
            const labels = vitalsData.map(item => {
                const date = new Date(item.date);
                return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
            });

            const lcpData = vitalsData.map(item => parseFloat(item.lcp) || 0);
            const fidData = vitalsData.map(item => parseInt(item.fid) || 0);
            const clsData = vitalsData.map(item => (parseFloat(item.cls) || 0) * 1000); // Scale CLS for visibility

            console.log('Chart labels:', labels);
            console.log('LCP data:', lcpData);
            console.log('FID data:', fidData);
            console.log('CLS data:', clsData);

            // Chart configuration - simplified for better compatibility
            const config = {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: 'LCP (seconds)',
                            data: lcpData,
                            borderColor: '#2196F3',
                            backgroundColor: 'rgba(33, 150, 243, 0.1)',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.3
                        },
                        {
                            label: 'FID (milliseconds)',
                            data: fidData,
                            borderColor: '#FF9800',
                            backgroundColor: 'rgba(255, 152, 0, 0.1)',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.3,
                            yAxisID: 'y1'
                        },
                        {
                            label: 'CLS (×1000)',
                            data: clsData,
                            borderColor: '#9C27B0',
                            backgroundColor: 'rgba(156, 39, 176, 0.1)',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.3
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: 'Date'
                            }
                        },
                        y: {
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: 'LCP (seconds)'
                            }
                        },
                        y1: {
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: 'FID (ms)'
                            },
                            grid: {
                                drawOnChartArea: false
                            }
                        }
                    }
                }
            };

            // Create the chart
            console.log('Creating chart with config:', config);
            const chart = new Chart(chartCanvas, config);

            // Store chart instance globally for updates
            window.coreWebVitalsChart = chart;

            console.log('Core Web Vitals chart created successfully:', chart);

            // Load initial data
            updateCoreWebVitalsChart().catch(function(error) {
                console.warn('Failed to load initial chart data:', error);
                // Chart is still functional, just no data yet
            });

        } catch (error) {
            console.error('Error initializing Core Web Vitals chart:', error);
            console.error('Error details:', error.message, error.stack);

            // Clear any existing chart reference
            window.coreWebVitalsChart = null;

            // Show fallback chart with simple HTML/CSS
            if (chartCanvas && chartCanvas.parentElement) {
                console.log('Creating fallback chart display...');
                createFallbackChart(chartCanvas.parentElement);
            }
        }
    }

    /**
     * Create fallback chart when Chart.js fails
     */
    function createFallbackChart(container) {
        const fallbackHTML = `
            <div style="padding: 20px; background: #f9f9f9; border-radius: 8px; text-align: center;">
                <h4 style="margin: 0 0 15px; color: #333;">Core Web Vitals Overview</h4>
                <div style="display: flex; justify-content: space-around; margin-bottom: 15px;">
                    <div style="text-align: center;">
                        <div style="font-size: 24px; font-weight: bold; color: #2196F3;">2.1s</div>
                        <div style="font-size: 12px; color: #666;">LCP (Largest Contentful Paint)</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 24px; font-weight: bold; color: #FF9800;">85ms</div>
                        <div style="font-size: 12px; color: #666;">FID (First Input Delay)</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 24px; font-weight: bold; color: #9C27B0;">0.05</div>
                        <div style="font-size: 12px; color: #666;">CLS (Cumulative Layout Shift)</div>
                    </div>
                </div>
                <div style="margin-top: 15px; padding: 10px; background: #fff3cd; border-radius: 4px; font-size: 12px; color: #856404;">
                    <strong>Chart library unavailable.</strong> Showing current metrics only.
                    <button onclick="location.reload()" style="margin-left: 10px; padding: 4px 8px; background: #4CAF50; color: white; border: none; border-radius: 3px; cursor: pointer; font-size: 11px;">Refresh</button>
                </div>
            </div>
        `;

        container.innerHTML = fallbackHTML;
    }

    /**
     * Test auto-update functionality
     */
    function testAutoUpdateFunctionality() {
        console.log('🔍 Redco Debug: Testing auto-update functionality...');
        console.log('🔍 Redco Debug: Current settings from PHP:', redcoAjax.settings);
        console.log('🔍 Redco Debug: Config object:', config);

        // Check if performance monitoring is active
        if (performanceUpdateActive) {
            console.log('✅ Redco Debug: Performance auto-update is ACTIVE');
            console.log('✅ Redco Debug: Update interval:', config.performanceUpdateInterval + 'ms (' + (config.performanceUpdateInterval/1000) + 's)');
            console.log('✅ Redco Debug: Timer ID:', performanceUpdateTimer);
            console.log('✅ Redco Debug: Next update in:', (config.performanceUpdateInterval/1000) + ' seconds');

        } else {
            console.log('❌ Redco Debug: Performance auto-update is INACTIVE');
            console.log('❌ Redco Debug: Checking reasons...');

            // Check if dashboard elements exist
            if (!$('.redco-performance-overview').length) {
                console.log('❌ Redco Debug: Performance overview element not found');
                console.log('❌ Redco Debug: Available elements:', $('.redco-performance-overview, .redco-performance-health-dashboard, [class*="performance"]').length);
            } else {
                console.log('✅ Redco Debug: Performance overview element found');
            }

            // Check if monitoring is enabled
            if (redcoAjax.settings && !redcoAjax.settings.monitoringEnabled) {
                console.log('❌ Redco Debug: Monitoring disabled in settings');
            } else {
                console.log('✅ Redco Debug: Monitoring enabled in settings');
            }

            // Try to manually start monitoring
            console.log('🔧 Redco Debug: Attempting to manually start performance monitoring...');
            initPerformanceMonitoring();
        }

        // Test manual performance update
        console.log('🔧 Redco Debug: Testing manual performance update...');
        updatePerformanceMetrics(true);

        // Test manual health update
        console.log('🔧 Redco Debug: Testing manual health update...');
        updateHealthMetrics().then(function(data) {
            console.log('✅ Redco Debug: Health metrics updated successfully:', data);
        }).catch(function(error) {
            console.log('❌ Redco Debug: Health metrics update failed:', error);
        });

        // Show auto-refresh time to user (in console only)
        displayAutoRefreshInfo();

        // Debug indicator can be shown manually via redcoDebug.showDebugIndicator()
        console.log('🔧 Redco Debug: Use redcoDebug.showDebugIndicator() to show visual indicator');
    }

    /**
     * Display auto-refresh information to user (console only)
     */
    function displayAutoRefreshInfo() {
        const refreshInterval = Math.round(config.performanceUpdateInterval / 1000);
        console.log('🔧 Redco Debug: Auto-refresh interval configured:', refreshInterval + 's');
        console.log('🔧 Redco Debug: Performance monitoring active:', performanceUpdateActive);
        console.log('🔧 Redco Debug: Timer ID:', performanceUpdateTimer);

        // No visual indicators added - only console logging for debugging
    }

    /**
     * Add debug indicator for auto-update status
     */
    function addDebugIndicator() {
        // Remove existing debug indicator
        $('.redco-debug-indicator').remove();

        const status = performanceUpdateActive ? 'ACTIVE' : 'INACTIVE';
        const color = performanceUpdateActive ? '#4CAF50' : '#f44336';
        const interval = Math.round(config.performanceUpdateInterval / 1000);

        const $debugIndicator = $(`
            <div class="redco-debug-indicator" style="
                position: fixed;
                top: 32px;
                right: 20px;
                background: ${color};
                color: white;
                padding: 8px 12px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: bold;
                z-index: 999999;
                box-shadow: 0 2px 8px rgba(0,0,0,0.3);
            ">
                Auto-Update: ${status} (${interval}s)
            </div>
        `);

        $('body').append($debugIndicator);

        // Auto-remove after 10 seconds
        setTimeout(function() {
            $debugIndicator.fadeOut(500, function() {
                $(this).remove();
            });
        }, 10000);
    }

    // Initialize exclude sections when DOM is ready
    $(document).ready(function() {
        // Add to existing initialization
        setTimeout(initExcludeSections, 100);
        initHealthMonitor();
    });

    /**
     * Initialize PageSpeed refresh functionality
     */
    function initPageSpeedRefresh() {
        // Current device strategy - initialize from DOM state
        let currentStrategy = getCurrentDeviceStrategy();

        // Device toggle functionality with optimized loading
        $(document).on('click', '.device-btn', function(e) {
            e.preventDefault();

            const $btn = $(this);
            const $toggle = $('.device-toggle');

            // Prevent clicks during auto-refresh
            if ($toggle.hasClass('auto-refreshing') || $btn.prop('disabled') || $btn.hasClass('disabled')) {
                console.log('Redco Debug: Device toggle blocked - auto-refresh in progress');
                return false;
            }

            const strategy = $btn.data('device');

            if (strategy === currentStrategy) return;

            // Update active state immediately for responsive UI
            $('.device-btn').removeClass('active');
            $btn.addClass('active');

            currentStrategy = strategy;
            console.log('Redco Debug: User switched to device strategy:', strategy);

            // Show immediate loading feedback
            showDeviceToggleLoading($btn, strategy);

            // Load scores asynchronously without blocking UI
            loadScoresForDeviceAsync(strategy);
        });

        // Refresh button functionality
        $(document).on('click', '.refresh-pagespeed-btn', function(e) {
            e.preventDefault();

            const $button = $(this);

            // Don't allow refresh if disabled
            if ($button.prop('disabled')) {
                showToast('Please add a PageSpeed Insights API key in settings first.', 'warning');
                return;
            }

            // Refresh scores for current strategy
            refreshPageSpeedScores(currentStrategy, true);
        });
    }

    /**
     * Show loading feedback for device toggle
     */
    function showDeviceToggleLoading($btn, strategy) {
        console.log('Redco Debug: Showing loading feedback for', strategy, 'toggle');

        // Add loading class to button
        $btn.addClass('loading');

        // Show loading indicators on score cards
        $('.score-item .score-circle .score-number').each(function() {
            const $scoreNumber = $(this);
            if (!$scoreNumber.hasClass('loading-scores')) {
                $scoreNumber.addClass('loading-scores');
                // Show a subtle loading animation
                triggerScoreLoadingAnimation($scoreNumber);
            }
        });

        // Show cached/estimated scores immediately if available
        showCachedOrEstimatedScores(strategy);
    }

    /**
     * Show cached or estimated scores immediately for responsive UI
     */
    function showCachedOrEstimatedScores(strategy) {
        console.log('Redco Debug: Showing cached/estimated scores for', strategy);

        // Try to get cached scores first
        const cacheKey = 'redco_pagespeed_scores_' + strategy;
        const cachedScores = sessionStorage.getItem(cacheKey);

        if (cachedScores) {
            try {
                const scores = JSON.parse(cachedScores);
                console.log('Redco Debug: Using cached scores for', strategy);
                updatePageSpeedScoresDisplay(scores);
                return;
            } catch (e) {
                console.warn('Redco Debug: Failed to parse cached scores');
            }
        }

        // Fall back to estimated scores for immediate feedback
        console.log('Redco Debug: Using estimated scores for immediate feedback');
        const estimatedScores = generateEstimatedScores(strategy);
        updatePageSpeedScoresDisplay(estimatedScores);
    }

    /**
     * Generate estimated scores for immediate display
     */
    function generateEstimatedScores(strategy) {
        // Base scores that vary slightly by device
        const baseScores = {
            mobile: { performance: 78, accessibility: 89, best_practices: 85, seo: 92 },
            desktop: { performance: 85, accessibility: 91, best_practices: 87, seo: 94 }
        };

        const scores = baseScores[strategy] || baseScores.mobile;

        return {
            performance_score: scores.performance,
            accessibility_score: scores.accessibility,
            best_practices_score: scores.best_practices,
            seo_score: scores.seo,
            is_estimated: true
        };
    }

    /**
     * Load scores for specific device asynchronously
     */
    function loadScoresForDeviceAsync(strategy) {
        console.log('Redco Debug: Loading scores asynchronously for', strategy);

        // Set a timeout for responsive UI (200ms max wait)
        const startTime = Date.now();

        $.ajax({
            url: redcoAjax.ajaxurl,
            type: 'POST',
            data: {
                action: 'redco_get_pagespeed_scores',
                strategy: strategy,
                nonce: redcoAjax.nonce
            },
            timeout: 15000, // 15 second timeout
            success: function(response) {
                const loadTime = Date.now() - startTime;
                console.log('Redco Debug: PageSpeed scores loaded in', loadTime + 'ms');

                if (response.success && response.data) {
                    // Cache the scores for future use
                    const cacheKey = 'redco_pagespeed_scores_' + strategy;
                    sessionStorage.setItem(cacheKey, JSON.stringify(response.data));

                    // Update scores with real data
                    updatePageSpeedScoresDisplay(response.data);

                    // Update last updated time
                    updateLastUpdatedTime();

                    console.log('Redco Debug: Real PageSpeed scores updated for', strategy);
                } else {
                    console.warn('Redco Debug: Invalid PageSpeed response, keeping estimated scores');
                }
            },
            error: function(xhr, status, error) {
                const loadTime = Date.now() - startTime;
                console.warn('Redco Debug: PageSpeed API failed after', loadTime + 'ms:', error);
                console.log('Redco Debug: Gracefully falling back to estimated scores');

                // Keep the estimated scores that were already shown
                // No need to update UI since estimated scores are already displayed
            },
            complete: function() {
                // Remove loading indicators
                hideDeviceToggleLoading();
            }
        });
    }

    /**
     * Hide loading feedback for device toggle
     */
    function hideDeviceToggleLoading() {
        console.log('Redco Debug: Hiding device toggle loading feedback');

        // Remove loading class from buttons
        $('.device-btn').removeClass('loading');

        // Remove loading indicators from score cards
        $('.score-item .score-circle .score-number').removeClass('loading-scores');
    }

    /**
     * Trigger subtle loading animation for score numbers
     */
    function triggerScoreLoadingAnimation($element) {
        // Add a subtle pulse effect while loading
        $element.css({
            'opacity': '0.7',
            'transition': 'opacity 0.5s ease-in-out'
        });

        // Remove the effect after a short time
        setTimeout(function() {
            $element.css({
                'opacity': '1',
                'transition': 'opacity 0.3s ease-in-out'
            });
        }, 800);
    }

    /**
     * Load scores for specific device (legacy function for compatibility)
     */
    function loadScoresForDevice(strategy) {
        // Redirect to async version for better performance
        loadScoresForDeviceAsync(strategy);
    }

    /**
     * Refresh PageSpeed scores for specific strategy
     */
    function refreshPageSpeedScores(strategy, showLoading = true) {
        const $button = $('.refresh-pagespeed-btn');

        if (showLoading) {
            // Show loading state
            $button.prop('disabled', true);
            $button.find('.dashicons').addClass('spin');

            // Trigger score animations
            $('.score-item .score-circle .score-number').each(function() {
                triggerScoreAnimation($(this));
            });
        }

        // Call refresh API
        $.ajax({
            url: redcoAjax.ajaxurl,
            type: 'POST',
            data: {
                action: 'redco_refresh_pagespeed_scores',
                strategy: strategy,
                nonce: redcoAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    if (showLoading) {
                        showToast(response.data.message, 'success');
                    }

                    // Update scores with new data
                    if (response.data.scores) {
                        updatePageSpeedScoresDisplay(response.data.scores);
                    }

                    // Update last updated time
                    updateLastUpdatedTime();

                    // Reload page after short delay to show updated scores (only for manual refresh)
                    if (showLoading) {
                        setTimeout(function() {
                            window.location.reload();
                        }, 2000);
                    }
                } else {
                    if (showLoading) {
                        showToast(response.data.message || 'Failed to refresh PageSpeed scores', 'error');
                    }
                }
            },
            error: function(xhr, status, error) {
                console.error('PageSpeed refresh error:', error);
                if (showLoading) {
                    showToast('Error refreshing PageSpeed scores. Please try again.', 'error');
                }
            },
            complete: function() {
                if (showLoading) {
                    // Restore button state
                    $button.prop('disabled', false);
                    $button.find('.dashicons').removeClass('spin');
                }
            }
        });
    }

    /**
     * Get current device strategy from DOM state
     */
    function getCurrentDeviceStrategy() {
        const $activeButton = $('.device-btn.active');
        if ($activeButton.length > 0) {
            const strategy = $activeButton.data('device');
            console.log('Redco Debug: Current device strategy from DOM:', strategy);
            return strategy;
        }

        // Default to mobile if no active button found
        console.log('Redco Debug: No active device button found, defaulting to mobile');
        return 'mobile';
    }

    /**
     * Disable device toggle during auto-refresh
     */
    function disableDeviceToggle() {
        const $toggle = $('.device-toggle');
        const $buttons = $('.device-btn');

        if ($toggle.length === 0) return; // No toggle present (no API key)

        console.log('Redco Debug: Disabling device toggle during auto-refresh');

        // Store the currently active device strategy before disabling
        const $activeButton = $buttons.filter('.active');
        if ($activeButton.length > 0) {
            const activeStrategy = $activeButton.data('device');
            $toggle.data('stored-active-strategy', activeStrategy);
            console.log('Redco Debug: Stored active device strategy:', activeStrategy);
        } else {
            // Default to mobile if no active button found
            $toggle.data('stored-active-strategy', 'mobile');
            console.log('Redco Debug: No active button found, defaulting to mobile');
        }

        // Add auto-refreshing class for visual feedback
        $toggle.addClass('auto-refreshing');

        // Disable individual buttons but preserve their visual state
        $buttons.prop('disabled', true).addClass('disabled');

        // Add loading cursor
        $toggle.css('cursor', 'not-allowed');
    }

    /**
     * Enable device toggle after auto-refresh completes
     */
    function enableDeviceToggle() {
        const $toggle = $('.device-toggle');
        const $buttons = $('.device-btn');

        if ($toggle.length === 0) return; // No toggle present (no API key)

        console.log('Redco Debug: Re-enabling device toggle after auto-refresh');

        // Restore the previously active device strategy
        const storedStrategy = $toggle.data('stored-active-strategy') || 'mobile';
        console.log('Redco Debug: Restoring active device strategy:', storedStrategy);

        // Remove active class from all buttons
        $buttons.removeClass('active');

        // Set the correct button as active based on stored strategy
        const $targetButton = $buttons.filter(`[data-device="${storedStrategy}"]`);
        if ($targetButton.length > 0) {
            $targetButton.addClass('active');
            console.log('Redco Debug: Restored active state to', storedStrategy, 'button');
        } else {
            // Fallback to mobile if target button not found
            $buttons.filter('[data-device="mobile"]').addClass('active');
            console.log('Redco Debug: Fallback - set mobile as active');
        }

        // Remove auto-refreshing class
        $toggle.removeClass('auto-refreshing');

        // Re-enable individual buttons
        $buttons.prop('disabled', false).removeClass('disabled');

        // Restore normal cursor
        $toggle.css('cursor', '');

        // Clear stored strategy data
        $toggle.removeData('stored-active-strategy');
    }

    /**
     * Update PageSpeed scores display with new data
     */
    function updatePageSpeedScoresDisplay(data) {
        // Update Performance Score
        updateScoreWithAnimation('.performance-score-item .score-circle', data.performance_score);

        // Update Accessibility Score
        updateScoreWithAnimation('.accessibility-score-item .score-circle', data.accessibility_score);

        // Update Best Practices Score
        updateScoreWithAnimation('.best-practices-score-item .score-circle', data.best_practices_score);

        // Update SEO Score
        updateScoreWithAnimation('.seo-score-item .score-circle', data.seo_score);
    }

    /**
     * Update score with animation
     */
    function updateScoreWithAnimation(selector, newScore) {
        const $scoreCircle = $(selector);
        if ($scoreCircle.length) {
            const $scoreNumber = $scoreCircle.find('.score-number');

            // Animate to new score
            animateNumberToTarget($scoreNumber, newScore);

            // Update data attribute
            $scoreCircle.attr('data-score', newScore);

            // Update CSS custom property for circle animation
            $scoreCircle.css('--score-percentage', newScore);
        }
    }

    /**
     * Update module statistics after actions
     */
    function updateModuleStatistics(action) {
        // Map actions to modules
        const actionModuleMap = {
            'optimize_database': 'database-cleanup',
            'database_cleanup': 'database-cleanup',
            'clear_cache': 'page-cache',
            'clear_page_cache': 'page-cache',
            'clear_minified_cache': 'css-js-minifier',
            'preload_cache': 'page-cache'
        };

        const module = actionModuleMap[action];
        if (!module) {
            return; // No module mapping for this action
        }

        // Get updated statistics
        $.ajax({
            url: redcoAjax.ajaxurl,
            type: 'POST',
            data: {
                action: 'redco_get_module_stats',
                module: module,
                nonce: redcoAjax.nonce
            },
            success: function(response) {
                if (response.success && response.data.stats) {
                    updateModuleStatsDisplay(module, response.data.stats);
                }
            },
            error: function(xhr, status, error) {
                console.warn('Failed to update module statistics:', error);
            }
        });
    }

    /**
     * Update module statistics display
     */
    function updateModuleStatsDisplay(module, stats) {
        switch (module) {
            case 'database-cleanup':
                updateDatabaseCleanupStats(stats);
                break;
            case 'css-js-minifier':
                updateMinifierStats(stats);
                break;
            case 'lazy-load':
                updateLazyLoadStats(stats);
                break;
            case 'page-cache':
                updatePageCacheStats(stats);
                break;
            case 'heartbeat-control':
                updateHeartbeatStats(stats);
                break;
            case 'autosave-reducer':
                updateAutosaveStats(stats);
                break;
        }
    }

    /**
     * Update database cleanup statistics display
     */
    function updateDatabaseCleanupStats(stats) {
        // Update counts in the cleanup options
        if (stats.revisions !== undefined) {
            $('.cleanup-option .count').each(function() {
                const $count = $(this);
                const $checkbox = $count.closest('.cleanup-option').find('input[type="checkbox"]');

                if ($checkbox.attr('name') === 'settings[cleanup_revisions]') {
                    $count.text('(' + (stats.revisions || 0).toLocaleString() + ')');
                } else if ($checkbox.attr('name') === 'settings[cleanup_auto_drafts]') {
                    $count.text('(' + (stats.auto_drafts || 0).toLocaleString() + ')');
                } else if ($checkbox.attr('name') === 'settings[cleanup_trashed_posts]') {
                    $count.text('(' + (stats.trashed_posts || 0).toLocaleString() + ')');
                } else if ($checkbox.attr('name') === 'settings[cleanup_spam_comments]') {
                    $count.text('(' + (stats.spam_comments || 0).toLocaleString() + ')');
                } else if ($checkbox.attr('name') === 'settings[cleanup_trashed_comments]') {
                    $count.text('(' + (stats.trashed_comments || 0).toLocaleString() + ')');
                } else if ($checkbox.attr('name') === 'settings[cleanup_expired_transients]') {
                    $count.text('(' + (stats.expired_transients || 0).toLocaleString() + ')');
                } else if ($checkbox.attr('name') === 'settings[cleanup_orphaned_postmeta]') {
                    $count.text('(' + (stats.orphaned_postmeta || 0).toLocaleString() + ')');
                } else if ($checkbox.attr('name') === 'settings[cleanup_orphaned_commentmeta]') {
                    $count.text('(' + (stats.orphaned_commentmeta || 0).toLocaleString() + ')');
                }
            });
        }

        // Add visual feedback
        $('.cleanup-option .count').addClass('updated');
        setTimeout(() => $('.cleanup-option .count').removeClass('updated'), 1000);
    }

    /**
     * Update minifier statistics display
     */
    function updateMinifierStats(stats) {
        $('.minifier-stats .stat-item').each(function() {
            const $item = $(this);
            const $value = $item.find('span');

            if ($item.find('strong').text().includes('Files Minified')) {
                $value.text((stats.files_minified || 0).toLocaleString());
            } else if ($item.find('strong').text().includes('Size Reduction')) {
                $value.text(formatBytes(stats.bytes_saved || 0));
            }
        });

        // Add visual feedback
        $('.minifier-stats .stat-item span').addClass('updated');
        setTimeout(() => $('.minifier-stats .stat-item span').removeClass('updated'), 1000);
    }

    /**
     * Update lazy load statistics display
     */
    function updateLazyLoadStats(stats) {
        $('.lazy-stats .stat-item').each(function() {
            const $item = $(this);
            const $value = $item.find('span');

            if ($item.find('strong').text().includes('Images Processed')) {
                $value.text((stats.images_processed || 0).toLocaleString());
            } else if ($item.find('strong').text().includes('Bandwidth Saved')) {
                $value.text(formatBytes(stats.bytes_saved || 0));
            }
        });

        // Add visual feedback
        $('.lazy-stats .stat-item span').addClass('updated');
        setTimeout(() => $('.lazy-stats .stat-item span').removeClass('updated'), 1000);
    }

    /**
     * Update page cache statistics display
     */
    function updatePageCacheStats(stats) {
        $('.cache-stats .stat-item').each(function() {
            const $item = $(this);
            const $value = $item.find('span');

            if ($item.find('strong').text().includes('Cache Hit Rate')) {
                $value.text((stats.hit_ratio || stats.hit_rate || 0) + '%');
            } else if ($item.find('strong').text().includes('Cached Pages')) {
                $value.text((stats.cached_pages || 0).toLocaleString());
            } else if ($item.find('strong').text().includes('Cache Size')) {
                $value.text(stats.cache_size || '0 Bytes');
            }
        });

        // Add visual feedback
        $('.cache-stats .stat-item span').addClass('updated');
        setTimeout(() => $('.cache-stats .stat-item span').removeClass('updated'), 1000);
    }

    /**
     * Update cache statistics after clearing cache
     */
    function updateCacheStatistics(stats) {
        // Update page cache statistics if on page cache page
        if (typeof updatePageCacheStats === 'function') {
            updatePageCacheStats(stats);
        }

        // Update cache statistics in sidebar if present
        $('.cache-stats .stat-item').each(function() {
            const $item = $(this);
            const $value = $item.find('span');

            if ($item.find('strong').text().includes('Cache Hit Rate')) {
                $value.text((stats.hit_ratio || stats.hit_rate || 0) + '%');
            } else if ($item.find('strong').text().includes('Cached Pages')) {
                $value.text((stats.cached_pages || 0).toLocaleString());
            } else if ($item.find('strong').text().includes('Cache Size')) {
                $value.text(stats.cache_size || '0 Bytes');
            }
        });

        // Add visual feedback
        $('.cache-stats .stat-item span').addClass('updated');
        setTimeout(() => {
            $('.cache-stats .stat-item span').removeClass('updated');
        }, 1000);
    }

    /**
     * Update heartbeat statistics display
     */
    function updateHeartbeatStats(stats) {
        // Update heartbeat status displays if they exist
        $('.heartbeat-stats .stat-item').each(function() {
            const $item = $(this);
            const $value = $item.find('span');

            if ($item.find('strong').text().includes('Admin Status')) {
                $value.text(stats.admin_status || 'Unknown');
            } else if ($item.find('strong').text().includes('Editor Status')) {
                $value.text(stats.editor_status || 'Unknown');
            } else if ($item.find('strong').text().includes('Frontend Status')) {
                $value.text(stats.frontend_status || 'Unknown');
            }
        });
    }

    /**
     * Update autosave statistics display
     */
    function updateAutosaveStats(stats) {
        // Update autosave status displays if they exist
        $('.autosave-stats .stat-item').each(function() {
            const $item = $(this);
            const $value = $item.find('span');

            if ($item.find('strong').text().includes('Current Interval')) {
                $value.text((stats.current_interval || 60) + 's');
            } else if ($item.find('strong').text().includes('Reduction')) {
                $value.text((stats.reduction_percentage || 0) + '%');
            }
        });
    }

    /**
     * Format bytes to human readable format
     */
    function formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Update last updated time display
     */
    function updateLastUpdatedTime() {
        const $lastUpdated = $('.last-updated');
        if ($lastUpdated.length) {
            $lastUpdated.text('Just now');
        }
    }

    // Expose functions for manual testing
    window.redcoDebug = {
        testAutoUpdate: testAutoUpdateFunctionality,
        startPerformanceMonitoring: function() {
            console.log('🔧 Manual: Starting performance monitoring...');
            initPerformanceMonitoring();
        },
        forceUpdateMetrics: function() {
            console.log('🔧 Manual: Forcing all dashboard sections update...');
            updateAllDashboardSections(true);
        },
        forceUpdatePerformanceOnly: function() {
            console.log('🔧 Manual: Forcing performance metrics only...');
            updatePerformanceMetrics(true);
        },
        forceUpdatePageSpeedScores: function() {
            console.log('🔧 Manual: Forcing PageSpeed scores update...');
            updateWebsitePerformanceScores();
        },
        forceUpdateCoreWebVitals: function() {
            console.log('🔧 Manual: Forcing Core Web Vitals update...');
            updateCoreWebVitalsChart();
        },
        checkTimerStatus: function() {
            console.log('🔍 Timer Status:');
            console.log('- Active:', performanceUpdateActive);
            console.log('- Timer ID:', performanceUpdateTimer);
            console.log('- Interval:', config.performanceUpdateInterval + 'ms (' + (config.performanceUpdateInterval/1000) + 's)');
            console.log('- Settings:', redcoAjax.settings);
            console.log('- Current URL:', window.location.href);
            console.log('- Is main dashboard:', window.location.href.includes('page=redco-optimizer') && !window.location.href.includes('&tab='));
            console.log('- Performance elements:', $('.redco-performance-overview, .redco-performance-health-dashboard, .performance-metric, .health-metric').length);

            if (performanceUpdateActive && performanceUpdateTimer) {
                console.log('✅ Timer is running - next update in ' + (config.performanceUpdateInterval/1000) + ' seconds');
            } else {
                console.log('❌ Timer is not running');
            }
        },
        restartAutoUpdate: function() {
            console.log('🔄 Restarting auto-update...');
            stopPerformanceUpdates();
            setTimeout(function() {
                startPerformanceUpdates();
                console.log('✅ Auto-update restarted. Use redcoDebug.showDebugIndicator() to show visual indicator.');
            }, 1000);
        },
        updateInterval: function(seconds) {
            console.log('🔧 Manual: Updating interval to', seconds, 'seconds');
            updatePerformanceInterval(seconds);
        },
        showDebugIndicator: function() {
            console.log('🔧 Manual: Showing debug indicator...');
            addDebugIndicator();
        },
        hideDebugIndicator: function() {
            console.log('🔧 Manual: Hiding debug indicator...');
            $('.redco-debug-indicator').remove();
        },
        testFormSubmission: function(moduleKey) {
            console.log('🔧 Testing form submission for module:', moduleKey || 'current');
            const $form = moduleKey ? $(`.redco-module-form[data-module="${moduleKey}"]`) : $('.redco-module-form:visible');

            if (!$form.length) {
                console.error('❌ No form found for module:', moduleKey);
                return;
            }

            console.log('✅ Form found:', $form);
            console.log('✅ Module:', $form.data('module'));
            console.log('✅ Form data:', $form.serializeArray());

            // Test form submission
            $form.trigger('submit');
        },
        listAllForms: function() {
            console.log('📋 All module forms on page:');
            $('.redco-module-form').each(function(index) {
                const $form = $(this);
                console.log(`${index + 1}. Module: ${$form.data('module')}, Visible: ${$form.is(':visible')}`);
            });
        },
        debugChart: function() {
            console.log('🔍 Chart.js Debug Information:');
            console.log('- Chart.js loaded:', typeof Chart !== 'undefined');
            console.log('- Chart.js version:', typeof Chart !== 'undefined' ? (Chart.version || 'unknown') : 'N/A');
            console.log('- Chart instance exists:', !!window.coreWebVitalsChart);
            console.log('- Chart canvas exists:', !!document.getElementById('coreWebVitalsChart'));

            if (typeof Chart !== 'undefined') {
                console.log('- Chart.js methods:', Object.keys(Chart));
                console.log('- Chart.register available:', typeof Chart.register === 'function');
                console.log('- Chart.Chart available:', typeof Chart.Chart === 'function');
            }

            if (window.coreWebVitalsChart) {
                console.log('- Chart instance type:', typeof window.coreWebVitalsChart);
                console.log('- Chart has destroy method:', typeof window.coreWebVitalsChart.destroy === 'function');
                console.log('- Chart has update method:', typeof window.coreWebVitalsChart.update === 'function');
                console.log('- Chart has data:', !!window.coreWebVitalsChart.data);
            }
        },
        reinitializeChart: function() {
            console.log('🔄 Manually reinitializing chart...');
            window.coreWebVitalsChart = null;
            initCoreWebVitalsChart();
        },
        createFallbackChart: function() {
            console.log('🔧 Creating fallback chart...');
            const container = document.getElementById('coreWebVitalsChart');
            if (container && container.parentElement) {
                createFallbackChart(container.parentElement);
            } else {
                console.error('Chart container not found');
            }
        },
        testPageSpeedAPI: function(strategy = 'mobile') {
            console.log('🔧 Testing PageSpeed API for strategy:', strategy);
            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_refresh_pagespeed_scores',
                    strategy: strategy,
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    console.log('✅ PageSpeed API Response:', response);
                    if (response.success && response.data.scores) {
                        console.log('📊 Scores received:', response.data.scores);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ PageSpeed API Error:', error);
                    console.error('Response:', xhr.responseText);
                }
            });
        },
        testDeviceToggle: function() {
            console.log('🔧 Testing device toggle functionality...');

            // Test mobile scores
            console.log('📱 Testing mobile scores...');
            loadScoresForDevice('mobile');

            setTimeout(function() {
                // Test desktop scores
                console.log('💻 Testing desktop scores...');
                loadScoresForDevice('desktop');
            }, 2000);
        },
        testDeviceToggleDisable: function() {
            console.log('🔧 Testing device toggle disable/enable...');

            console.log('🚫 Disabling device toggle...');
            disableDeviceToggle();

            setTimeout(function() {
                console.log('✅ Re-enabling device toggle...');
                enableDeviceToggle();
            }, 3000);
        },
        forceDisableDeviceToggle: function() {
            console.log('🚫 Manually disabling device toggle...');
            disableDeviceToggle();
        },
        forceEnableDeviceToggle: function() {
            console.log('✅ Manually enabling device toggle...');
            enableDeviceToggle();
        },
        testDeviceStatePreservation: function() {
            console.log('🔧 Testing device state preservation during auto-refresh...');

            const currentStrategy = getCurrentDeviceStrategy();
            console.log('📱 Current strategy before test:', currentStrategy);

            // Switch to desktop if currently mobile, or vice versa
            const testStrategy = currentStrategy === 'mobile' ? 'desktop' : 'mobile';
            console.log('🔄 Switching to:', testStrategy);

            // Simulate user clicking the other button
            $(`.device-btn[data-device="${testStrategy}"]`).trigger('click');

            setTimeout(function() {
                console.log('🚫 Disabling toggle (simulating auto-refresh)...');
                disableDeviceToggle();

                setTimeout(function() {
                    console.log('✅ Re-enabling toggle (simulating auto-refresh complete)...');
                    enableDeviceToggle();

                    const finalStrategy = getCurrentDeviceStrategy();
                    console.log('📱 Final strategy after test:', finalStrategy);

                    if (finalStrategy === testStrategy) {
                        console.log('✅ SUCCESS: Device state preserved correctly!');
                    } else {
                        console.log('❌ FAILURE: Device state was not preserved!');
                    }
                }, 2000);
            }, 1000);
        },
        getCurrentDeviceStrategy: function() {
            const strategy = getCurrentDeviceStrategy();
            console.log('📱 Current device strategy:', strategy);
            return strategy;
        },
        switchToMobile: function() {
            console.log('📱 Switching to mobile...');
            $('.device-btn[data-device="mobile"]').trigger('click');
        },
        switchToDesktop: function() {
            console.log('💻 Switching to desktop...');
            $('.device-btn[data-device="desktop"]').trigger('click');
        },
        testAsyncDeviceToggle: function() {
            console.log('🔧 Testing asynchronous device toggle loading...');

            const startTime = Date.now();
            console.log('⏱️ Starting desktop toggle test...');

            // Switch to desktop and measure response time
            $('.device-btn[data-device="desktop"]').trigger('click');

            const responseTime = Date.now() - startTime;
            console.log('⚡ Device toggle response time:', responseTime + 'ms');

            if (responseTime < 200) {
                console.log('✅ SUCCESS: Toggle responded within 200ms target');
            } else {
                console.log('⚠️ WARNING: Toggle response time exceeded 200ms target');
            }

            // Check if estimated scores are shown immediately
            setTimeout(function() {
                const hasEstimatedScores = $('.score-number').length > 0;
                console.log('📊 Estimated scores displayed:', hasEstimatedScores ? 'YES' : 'NO');
            }, 100);
        },
        // testProgressModalDesign function removed - progress modal system removed
        testCachedScores: function() {
            console.log('🔧 Testing cached scores functionality...');

            // Clear existing cache
            sessionStorage.removeItem('redco_pagespeed_scores_mobile');
            sessionStorage.removeItem('redco_pagespeed_scores_desktop');

            console.log('🗑️ Cleared existing cache');

            // Test mobile scores caching
            console.log('📱 Testing mobile scores caching...');
            $('.device-btn[data-device="mobile"]').trigger('click');

            setTimeout(function() {
                const mobileCache = sessionStorage.getItem('redco_pagespeed_scores_mobile');
                console.log('📱 Mobile cache status:', mobileCache ? 'CACHED' : 'NOT CACHED');

                // Test desktop scores caching
                console.log('💻 Testing desktop scores caching...');
                $('.device-btn[data-device="desktop"]').trigger('click');

                setTimeout(function() {
                    const desktopCache = sessionStorage.getItem('redco_pagespeed_scores_desktop');
                    console.log('💻 Desktop cache status:', desktopCache ? 'CACHED' : 'NOT CACHED');

                    // Test cache retrieval speed
                    const cacheStartTime = Date.now();
                    $('.device-btn[data-device="mobile"]').trigger('click');
                    const cacheResponseTime = Date.now() - cacheStartTime;

                    console.log('⚡ Cache retrieval time:', cacheResponseTime + 'ms');

                    if (cacheResponseTime < 50) {
                        console.log('✅ SUCCESS: Cache retrieval is very fast');
                    }
                }, 2000);
            }, 2000);
        },
        testDatabaseCleanupOptions: function() {
            console.log('🔧 Testing database cleanup options retrieval...');
            const options = getDatabaseCleanupOptions();
            console.log('📊 Database cleanup options:', options);

            // Test if form exists
            const $dbForm = $('.redco-module-form[data-module="database-cleanup"]');
            console.log('📋 Database cleanup form found:', $dbForm.length > 0);

            if ($dbForm.length) {
                console.log('📝 Form data:', $dbForm.serializeArray());
            }

            return options;
        },
        testOptimizeDB: function() {
            console.log('🔧 Testing Optimize DB button functionality...');
            const options = getDatabaseCleanupOptions();
            console.log('📊 Options that will be sent:', options);

            // Simulate the AJAX call
            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_database_cleanup',
                    options: options,
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    console.log('✅ Database cleanup test response:', response);
                },
                error: function(xhr, status, error) {
                    console.error('❌ Database cleanup test error:', error);
                    console.error('Response:', xhr.responseText);
                }
            });
        },
        testModuleStats: function(module = 'database-cleanup') {
            console.log('🔧 Testing module statistics update for:', module);

            // Test getting module stats
            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_get_module_stats',
                    module: module,
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    console.log('✅ Module stats response:', response);
                    if (response.success && response.data.stats) {
                        console.log('📊 Stats received:', response.data.stats);
                        updateModuleStatsDisplay(module, response.data.stats);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ Module stats error:', error);
                    console.error('Response:', xhr.responseText);
                }
            });
        },
        testStatsUpdate: function() {
            console.log('🔧 Testing statistics update animation...');

            // Test the animation on cleanup counts
            $('.cleanup-option .count').each(function() {
                const $count = $(this);
                const currentText = $count.text();
                const newCount = Math.floor(Math.random() * 100);

                console.log('📊 Updating count from', currentText, 'to', newCount);
                $count.text('(' + newCount + ')');
                $count.addClass('updated');

                setTimeout(() => $count.removeClass('updated'), 1000);
            });
        },
        simulateCleanup: function() {
            console.log('🔧 Simulating database cleanup...');

            // Simulate the cleanup process
            updateModuleStatistics('database_cleanup');

            // Show a test notification
            showNotice('Database cleanup simulation completed', 'success');
        },
        testCountUpdate: function() {
            console.log('🔧 Testing cleanup count updates...');

            // Test if we're on the database cleanup page
            if (typeof updateCleanupCounts === 'function') {
                console.log('✅ updateCleanupCounts function is available');

                // Simulate cleanup results
                const mockResults = {
                    revisions_deleted: 5,
                    auto_drafts_deleted: 3,
                    trashed_posts_deleted: 2,
                    spam_comments_deleted: 10,
                    trashed_comments_deleted: 1,
                    expired_transients_deleted: 15,
                    orphaned_postmeta_deleted: 8,
                    orphaned_commentmeta_deleted: 4
                };

                console.log('📊 Simulating count update with results:', mockResults);
                updateCleanupCounts(mockResults);
            } else {
                console.log('❌ updateCleanupCounts function not available (not on database cleanup page)');
            }
        },
        refreshCleanupStats: function() {
            console.log('🔧 Refreshing cleanup statistics...');

            // Get fresh stats
            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_get_module_stats',
                    module: 'database-cleanup',
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    console.log('✅ Fresh cleanup stats:', response);
                    if (response.success && response.data.stats) {
                        console.log('📊 Current database counts:', response.data.stats);

                        // Update display if on cleanup page
                        if (typeof updateCountsDisplay === 'function') {
                            updateCountsDisplay(response.data.stats);
                        }
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ Failed to get cleanup stats:', error);
                }
            });
        },
        testClearCache: function() {
            console.log('🔧 Testing Clear Cache functionality...');

            // Test the clear cache AJAX call
            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_clear_cache',
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    console.log('✅ Clear cache test response:', response);
                    if (response.success) {
                        console.log('📊 Cache clearing results:', response.data);
                        if (response.data.cleared_types) {
                            console.log('🗑️ Cleared cache types:', response.data.cleared_types);
                        }
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ Clear cache test error:', error);
                    console.error('Response:', xhr.responseText);
                }
            });
        },
        testClearPageCache: function() {
            console.log('🔧 Testing Clear Page Cache functionality...');

            // Test the clear page cache AJAX call
            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_clear_page_cache',
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    console.log('✅ Clear page cache test response:', response);
                    if (response.success) {
                        console.log('📊 Page cache clearing results:', response.data);
                        if (response.data.stats) {
                            console.log('📈 Updated cache stats:', response.data.stats);
                        }
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ Clear page cache test error:', error);
                    console.error('Response:', xhr.responseText);
                }
            });
        },
        testCacheStatistics: function() {
            console.log('🔧 Testing cache statistics update...');

            // Mock cache statistics
            const mockStats = {
                hit_ratio: 0,
                cached_pages: 0,
                cache_size: '0 Bytes'
            };

            console.log('📊 Simulating cache stats update with:', mockStats);
            updateCacheStatistics(mockStats);
        },
        simulateCacheClear: function() {
            console.log('🔧 Simulating cache clear action...');

            // Simulate the cache clear process
            updateModuleStatistics('clear_cache');

            // Show a test notification
            showNotice('Cache clear simulation completed', 'success');
        },
        // testModalResponsive function removed - progress modal system removed

        // CSS Debugging and Validation Functions
        debugCSSLoading: function() {
            console.log('🔧 Debugging CSS loading...');

            // Check if redcoAjax debug info is available
            if (typeof redcoAjax !== 'undefined' && redcoAjax.debug) {
                console.log('📋 CSS Debug Info:', redcoAjax.debug);
                console.log('🎨 CSS Version:', redcoAjax.debug.cssVersion);
                console.log('🔗 CSS URL:', redcoAjax.debug.cssUrl);
            }

            // Check if CSS files are loaded
            const cssLinks = document.querySelectorAll('link[rel="stylesheet"]');
            const redcoCSSLinks = Array.from(cssLinks).filter(link =>
                link.href.includes('redco-optimizer') ||
                link.href.includes('admin-style.css') ||
                link.href.includes('enhanced-ui.css')
            );

            console.log('🎨 Found Redco CSS files:', redcoCSSLinks.length);
            redcoCSSLinks.forEach((link, index) => {
                console.log(`📄 CSS ${index + 1}:`, {
                    href: link.href,
                    loaded: link.sheet !== null,
                    rules: link.sheet ? link.sheet.cssRules.length : 'N/A'
                });
            });

            // Progress modal CSS validation removed - progress modal system removed
        },

        // validateProgressModalCSS function removed - progress modal system removed

        // testCSSSpecificity function removed - progress modal system removed

        forceCSSReload: function() {
            console.log('🔧 Force reloading CSS files...');

            const cssLinks = document.querySelectorAll('link[rel="stylesheet"]');
            const redcoCSSLinks = Array.from(cssLinks).filter(link =>
                link.href.includes('redco-optimizer')
            );

            redcoCSSLinks.forEach(link => {
                const newLink = link.cloneNode();
                const url = new URL(link.href);
                url.searchParams.set('cache-bust', Date.now());
                newLink.href = url.toString();

                newLink.onload = () => {
                    console.log('✅ Reloaded CSS:', newLink.href);
                    link.remove();
                };

                link.parentNode.insertBefore(newLink, link.nextSibling);
            });
        },

        showCSSDebugNotice: function(missingClasses) {
            // Remove existing notice
            $('.redco-css-debug-notice').remove();

            const noticeHtml = `
                <div class="redco-css-debug-notice">
                    <button class="debug-close">&times;</button>
                    <strong>CSS Loading Issue Detected</strong><br>
                    Missing styles for: ${missingClasses.slice(0, 3).join(', ')}${missingClasses.length > 3 ? '...' : ''}<br>
                    <small>Check browser console for details</small>
                </div>
            `;

            $('body').append(noticeHtml);

            setTimeout(() => {
                $('.redco-css-debug-notice').addClass('show');
            }, 100);

            // Auto-hide after 10 seconds
            setTimeout(() => {
                $('.redco-css-debug-notice').removeClass('show');
                setTimeout(() => {
                    $('.redco-css-debug-notice').remove();
                }, 300);
            }, 10000);

            // Close button handler
            $(document).on('click', '.redco-css-debug-notice .debug-close', function() {
                $('.redco-css-debug-notice').removeClass('show');
                setTimeout(() => {
                    $('.redco-css-debug-notice').remove();
                }, 300);
            });
        },

        runFullDiagnostics: function() {
            console.log('🔧 Running full CSS diagnostics...');

            // Run all diagnostic functions
            this.debugCSSLoading();
            this.testCSSSpecificity();

            // Run server-side diagnostics
            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_css_troubleshoot',
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        console.log('🔍 Server-side diagnostics:', response.data.diagnostics);
                        window.redcoDebug.displayDiagnosticsReport(response.data.diagnostics);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ Diagnostics error:', error);
                }
            });
        },

        displayDiagnosticsReport: function(diagnostics) {
            console.log('📊 CSS Diagnostics Report:');
            console.log('='.repeat(50));

            // File status
            console.log('📁 CSS Files:');
            Object.keys(diagnostics.files).forEach(file => {
                const info = diagnostics.files[file];
                console.log(`  ${file}: ${info.exists ? '✅' : '❌'} exists, ${info.readable ? '✅' : '❌'} readable, ${info.size} bytes`);
            });

            // Enqueued styles
            console.log('\n🎨 Enqueued Styles:');
            Object.keys(diagnostics.enqueued).forEach(handle => {
                const style = diagnostics.enqueued[handle];
                console.log(`  ${handle}: v${style.version}, deps: [${style.deps.join(', ')}]`);
            });

            // Conflicts
            if (diagnostics.conflicts.length > 0) {
                console.log('\n⚠️ Potential Conflicts:');
                diagnostics.conflicts.forEach(conflict => {
                    console.log(`  ${conflict.name}: ${conflict.potential_issue}`);
                });
            }

            // Cache plugins
            if (diagnostics.cache_plugins.length > 0) {
                console.log('\n🗄️ Cache Plugins:');
                diagnostics.cache_plugins.forEach(plugin => {
                    console.log(`  ${plugin}`);
                });
            }

            console.log('='.repeat(50));
        },

        clearAllCaches: function() {
            console.log('🔧 Clearing all caches...');

            // Clear browser cache for CSS files
            this.forceCSSReload();

            // Clear server-side caches
            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_clear_css_cache',
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        console.log('✅ Server caches cleared:', response.data.cleared);

                        // Show success notice
                        const notice = $(`
                            <div class="notice notice-success is-dismissible">
                                <p><strong>Cache Cleared:</strong> ${response.data.cleared.join(', ')}</p>
                            </div>
                        `);
                        $('.wrap').prepend(notice);

                        // Auto-dismiss after 5 seconds
                        setTimeout(() => {
                            notice.fadeOut();
                        }, 5000);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ Cache clear error:', error);
                }
            });
        },

        // Performance Analysis Functions
        analyzePerformance: function() {
            console.log('🔧 Running performance analysis...');

            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_analyze_performance',
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        console.log('📊 Performance Analysis Results:');
                        console.log('='.repeat(50));

                        const analysis = response.data.analysis;
                        const recommendations = response.data.recommendations;

                        // Display analysis results
                        Object.keys(analysis).forEach(category => {
                            console.log(`\n📋 ${category.toUpperCase()}:`);
                            const data = analysis[category];

                            if (data.issues && data.issues.length > 0) {
                                console.log('❌ Issues found:');
                                data.issues.forEach(issue => {
                                    console.log(`  - ${issue}`);
                                });
                            } else {
                                console.log('✅ No issues detected');
                            }
                        });

                        // Display recommendations
                        if (recommendations.length > 0) {
                            console.log('\n💡 RECOMMENDATIONS:');
                            recommendations.forEach(rec => {
                                const priority = rec.priority === 'high' ? '🔴' :
                                               rec.priority === 'medium' ? '🟡' : '🟢';
                                console.log(`${priority} ${rec.recommendation}`);
                                console.log(`   Issue: ${rec.issue}`);
                            });
                        }

                        console.log('='.repeat(50));
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ Performance analysis error:', error);
                }
            });
        },

        checkFrontendAssets: function() {
            console.log('🔧 Checking frontend asset loading...');

            // This would need to be run on frontend, not admin
            console.log('⚠️ This function should be run on the frontend to check for admin assets loading incorrectly');
            console.log('💡 To test: Open your website frontend, open console, and run: redcoDebug.checkFrontendAssets()');
        },

        measurePageSpeed: function() {
            console.log('🔧 Measuring page performance...');

            if (window.performance && window.performance.timing) {
                const timing = window.performance.timing;
                const metrics = {
                    'DNS Lookup': timing.domainLookupEnd - timing.domainLookupStart,
                    'TCP Connection': timing.connectEnd - timing.connectStart,
                    'Server Response': timing.responseEnd - timing.requestStart,
                    'DOM Processing': timing.domComplete - timing.domLoading,
                    'Page Load': timing.loadEventEnd - timing.navigationStart
                };

                console.log('⏱️ Performance Metrics:');
                Object.keys(metrics).forEach(metric => {
                    console.log(`  ${metric}: ${metrics[metric]}ms`);
                });

                // Check for performance issues
                if (metrics['Server Response'] > 1000) {
                    console.warn('⚠️ Slow server response time detected');
                }

                if (metrics['DOM Processing'] > 2000) {
                    console.warn('⚠️ Slow DOM processing detected');
                }
            } else {
                console.log('❌ Performance timing API not available');
            }
        },

        // PageSpeed API Debugging Functions
        diagnosePageSpeedAPI: function() {
            console.log('🔧 Running PageSpeed API diagnostics...');

            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_diagnose_pagespeed_api',
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        console.log('📊 PageSpeed API Diagnostics Results:');
                        console.log('='.repeat(50));

                        const diagnostics = response.data.diagnostics;
                        const recommendations = response.data.recommendations;

                        // Display diagnostics by category
                        Object.keys(diagnostics).forEach(category => {
                            const data = diagnostics[category];
                            const statusIcon = data.status === 'good' ? '✅' :
                                             data.status === 'warning' ? '⚠️' : '❌';

                            console.log(`\n${statusIcon} ${category.toUpperCase()}:`);

                            if (data.issues && data.issues.length > 0) {
                                data.issues.forEach(issue => {
                                    console.log(`  - ${issue}`);
                                });
                            } else {
                                console.log('  No issues detected');
                            }
                        });

                        // Display recommendations
                        if (recommendations.length > 0) {
                            console.log('\n💡 RECOMMENDATIONS:');
                            recommendations.forEach(rec => {
                                const priority = rec.priority === 'high' ? '🔴' :
                                               rec.priority === 'medium' ? '🟡' : '🟢';
                                console.log(`${priority} [${rec.category}] ${rec.solution}`);
                                console.log(`   Issue: ${rec.issue}`);
                            });
                        }

                        console.log('='.repeat(50));
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ PageSpeed API diagnostics error:', error);
                }
            });
        },

        testPageSpeedAPICall: function(strategy = 'mobile', url = null) {
            console.log(`🔧 Testing PageSpeed API call for ${strategy}...`);

            if (!url) {
                url = window.location.origin;
            }

            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_test_pagespeed_api',
                    strategy: strategy,
                    url: url,
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        console.log('✅ PageSpeed API Test Results:');
                        console.log('📊 Test Details:', response.data.details);

                        if (response.data.success) {
                            console.log('🎯 Scores:', response.data.scores);
                        } else {
                            console.error('❌ API Test Failed:', response.data.error);
                        }
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ PageSpeed API test error:', error);
                }
            });
        },

        validateAPIKey: function(apiKey = null) {
            console.log('🔧 Validating PageSpeed API key...');

            if (!apiKey) {
                // Try to get from settings form if available
                const apiKeyInput = document.querySelector('input[name="redco_optimizer_performance[pagespeed_api_key]"]');
                if (apiKeyInput) {
                    apiKey = apiKeyInput.value;
                }
            }

            if (!apiKey) {
                console.error('❌ No API key provided for validation');
                return;
            }

            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_validate_api_key',
                    api_key: apiKey,
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        console.log('✅ API Key Validation: ' + response.data.message);
                    } else {
                        console.error('❌ API Key Validation Failed: ' + response.data.message);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ API key validation error:', error);
                }
            });
        },

        checkPageSpeedErrors: function() {
            console.log('🔧 Checking recent PageSpeed API errors...');

            // This would need a separate AJAX endpoint to get stored errors
            console.log('💡 Check WordPress error logs for detailed PageSpeed API error messages');
            console.log('📁 Look for entries starting with "Redco Optimizer: PageSpeed API"');
        },

        runPageSpeedFullDiagnostics: function() {
            console.log('🚀 Running complete PageSpeed API diagnostics...');
            console.log('='.repeat(60));

            // Run all diagnostic functions
            this.diagnosePageSpeedAPI();

            setTimeout(() => {
                this.testPageSpeedAPICall('mobile');
            }, 2000);

            setTimeout(() => {
                this.testPageSpeedAPICall('desktop');
            }, 4000);

            setTimeout(() => {
                this.validateAPIKey();
            }, 6000);

            console.log('⏱️ Running tests with delays to avoid rate limiting...');
        },

        // Module Auditing Functions
        auditAllModules: function() {
            console.log('🔧 Running comprehensive module audit...');

            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_audit_modules',
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        console.log('📊 Module Audit Results:');
                        console.log('='.repeat(50));

                        const audit_results = response.data.audit_results;
                        const recommendations = response.data.recommendations;

                        // Display audit results by module
                        Object.keys(audit_results).forEach(module => {
                            const audit = audit_results[module];
                            const statusIcon = audit.enabled ? '✅' : '❌';
                            const workingIcon = audit.instantiable ? '✅' : '❌';
                            const statsIcon = audit.stats_real ? '✅' : '❌';

                            console.log(`\n${statusIcon} ${audit.name.toUpperCase()}:`);
                            console.log(`  Enabled: ${audit.enabled ? 'Yes' : 'No'}`);
                            console.log(`  Working: ${workingIcon} ${audit.instantiable ? 'Yes' : 'No'}`);
                            console.log(`  Real Stats: ${statsIcon} ${audit.stats_real ? 'Yes' : 'No'}`);
                            console.log(`  Performance Impact: ${audit.performance_impact}`);

                            if (audit.issues.length > 0) {
                                console.log(`  Issues: ${audit.issues.join(', ')}`);
                            }
                        });

                        // Display recommendations
                        if (recommendations.length > 0) {
                            console.log('\n💡 OPTIMIZATION RECOMMENDATIONS:');
                            recommendations.forEach(rec => {
                                const priority = rec.priority === 'high' ? '🔴' :
                                               rec.priority === 'medium' ? '🟡' : '🟢';
                                console.log(`${priority} [${rec.module_name}] ${rec.message}`);
                            });
                        }

                        console.log('='.repeat(50));
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ Module audit error:', error);
                }
            });
        },

        testModuleAction: function(module, action) {
            console.log(`🔧 Testing ${module} action: ${action}...`);

            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_test_module_action',
                    module: module,
                    action: action,
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        console.log(`✅ ${module} ${action} test results:`);
                        console.log('📊 Execution time:', response.data.execution_time);

                        if (response.data.before_stats) {
                            console.log('📈 Before stats:', response.data.before_stats);
                        }

                        if (response.data.after_stats) {
                            console.log('📈 After stats:', response.data.after_stats);
                        }

                        if (response.data.action_result) {
                            console.log('🎯 Action result:', response.data.action_result);
                        }
                    } else {
                        console.error(`❌ ${module} ${action} test failed:`, response.data.message);
                    }
                },
                error: function(xhr, status, error) {
                    console.error(`❌ ${module} ${action} test error:`, error);
                }
            });
        },

        getRealModuleStats: function(module) {
            console.log(`🔧 Getting real statistics for ${module}...`);

            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_get_real_stats',
                    module: module,
                    nonce: redcoAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        console.log(`📊 Real ${module} statistics:`, response.data.stats);
                    } else {
                        console.error(`❌ Failed to get ${module} stats`);
                    }
                },
                error: function(xhr, status, error) {
                    console.error(`❌ ${module} stats error:`, error);
                }
            });
        },

        testAllModuleActions: function() {
            console.log('🚀 Testing all module actions...');
            console.log('='.repeat(60));

            // Test database cleanup
            setTimeout(() => {
                this.testModuleAction('database-cleanup', 'run_cleanup');
            }, 1000);

            // Test page cache
            setTimeout(() => {
                this.testModuleAction('page-cache', 'clear_all_cache');
            }, 3000);

            // Test CSS/JS minifier
            setTimeout(() => {
                this.testModuleAction('css-js-minifier', 'clear_minified_cache');
            }, 5000);

            // Get real stats for all modules
            setTimeout(() => {
                ['database-cleanup', 'page-cache', 'css-js-minifier', 'lazy-load', 'heartbeat-control'].forEach((module, index) => {
                    setTimeout(() => {
                        this.getRealModuleStats(module);
                    }, index * 1000);
                });
            }, 7000);

            console.log('⏱️ Running tests with delays to prevent conflicts...');
        },

        runCompleteAudit: function() {
            console.log('🚀 Running complete plugin audit...');
            console.log('='.repeat(60));

            // Run module audit first
            this.auditAllModules();

            // Then test PageSpeed API
            setTimeout(() => {
                this.runPageSpeedFullDiagnostics();
            }, 3000);

            // Then test module actions
            setTimeout(() => {
                this.testAllModuleActions();
            }, 8000);

            // Finally run performance analysis
            setTimeout(() => {
                this.analyzePerformance();
            }, 15000);

            console.log('⏱️ Complete audit will take ~20 seconds...');
        }
    };

    // Auto-run CSS validation on page load (only in debug mode)
    if (typeof redcoAjax !== 'undefined' && redcoAjax.debug) {
        setTimeout(() => {
            console.log('🔍 Auto-running CSS validation...');
            window.redcoDebug.debugCSSLoading();
        }, 1000);
    }

    // All initialization is already handled in the main $(document).ready() block above

    /**
     * Initialize module page optimizations for better UX
     */
    function initModulePageOptimizations() {
        // Only run on module pages
        if (!$('.redco-module-content').length) {
            return;
        }

        initCollapsibleSections();
        initViewModeControls();
        initSectionNavigation();
        initReadingProgress();
        initSmartDefaults();
    }

    /**
     * Initialize collapsible sections
     */
    function initCollapsibleSections() {
        // Make cards collapsible
        $('.redco-card').each(function() {
            const $card = $(this);
            const $header = $card.find('.card-header');

            if ($header.length) {
                $card.addClass('collapsible');

                // Add click handler
                $header.on('click', function(e) {
                    // Don't collapse if clicking on buttons
                    if ($(e.target).is('button, .button, input, select, textarea')) {
                        return;
                    }

                    $card.toggleClass('collapsed');

                    // Save state
                    const cardId = $card.attr('id') || $card.find('h3').text().trim();
                    localStorage.setItem('redco_card_' + cardId, $card.hasClass('collapsed'));
                });

                // Restore state
                const cardId = $card.attr('id') || $card.find('h3').text().trim();
                const isCollapsed = localStorage.getItem('redco_card_' + cardId) === 'true';
                if (isCollapsed) {
                    $card.addClass('collapsed');
                }
            }
        });
    }

    /**
     * Initialize view mode controls
     */
    function initViewModeControls() {
        // Create view mode controls
        const $controls = $(`
            <div class="view-mode-controls">
                <button class="view-mode-btn" data-mode="normal" title="Normal View">
                    <span class="dashicons dashicons-list-view"></span>
                </button>
                <button class="view-mode-btn" data-mode="compact" title="Compact View">
                    <span class="dashicons dashicons-grid-view"></span>
                </button>
                <button class="view-mode-btn" data-mode="collapse-all" title="Collapse All">
                    <span class="dashicons dashicons-minus"></span>
                </button>
                <button class="view-mode-btn" data-mode="expand-all" title="Expand All">
                    <span class="dashicons dashicons-plus-alt"></span>
                </button>
            </div>
        `);

        $('body').append($controls);

        // Handle view mode changes
        $('.view-mode-btn').on('click', function() {
            const mode = $(this).data('mode');
            const $content = $('.redco-module-content');

            $('.view-mode-btn').removeClass('active');
            $(this).addClass('active');

            switch(mode) {
                case 'compact':
                    $content.addClass('compact-mode');
                    localStorage.setItem('redco_view_mode', 'compact');
                    break;
                case 'normal':
                    $content.removeClass('compact-mode');
                    localStorage.setItem('redco_view_mode', 'normal');
                    break;
                case 'collapse-all':
                    $('.redco-card.collapsible').addClass('collapsed');
                    $('.redco-card.collapsible').each(function() {
                        const cardId = $(this).attr('id') || $(this).find('h3').text().trim();
                        localStorage.setItem('redco_card_' + cardId, 'true');
                    });
                    break;
                case 'expand-all':
                    $('.redco-card.collapsible').removeClass('collapsed');
                    $('.redco-card.collapsible').each(function() {
                        const cardId = $(this).attr('id') || $(this).find('h3').text().trim();
                        localStorage.setItem('redco_card_' + cardId, 'false');
                    });
                    break;
            }
        });

        // Restore view mode
        const savedMode = localStorage.getItem('redco_view_mode');
        if (savedMode === 'compact') {
            $('.redco-module-content').addClass('compact-mode');
            $('.view-mode-btn[data-mode="compact"]').addClass('active');
        } else {
            $('.view-mode-btn[data-mode="normal"]').addClass('active');
        }
    }

    /**
     * Initialize section navigation
     */
    function initSectionNavigation() {
        const $cards = $('.redco-card');
        if ($cards.length < 3) return; // Don't show nav for short pages

        // Create navigation
        const $nav = $('<div class="section-navigation"></div>');
        const $navTitle = $('<div class="section-nav-title">Quick Navigation</div>');
        const $navLinks = $('<div class="section-nav-links"></div>');

        $cards.each(function(index) {
            const $card = $(this);
            const title = $card.find('h3').text().trim();
            const id = 'section-' + index;

            $card.attr('id', id);

            const $link = $(`<a href="#${id}" class="section-nav-link">${title}</a>`);
            $navLinks.append($link);
        });

        $nav.append($navTitle, $navLinks);
        $('.redco-content-main').prepend($nav);

        // Handle navigation clicks
        $('.section-nav-link').on('click', function(e) {
            e.preventDefault();
            const target = $(this).attr('href');
            const $target = $(target);

            if ($target.length) {
                // Expand section if collapsed
                $target.removeClass('collapsed');

                // Smooth scroll
                $('html, body').animate({
                    scrollTop: $target.offset().top - 100
                }, 500);

                // Update active state
                $('.section-nav-link').removeClass('active');
                $(this).addClass('active');
            }
        });
    }

    /**
     * Initialize reading progress indicator
     */
    function initReadingProgress() {
        const $progress = $(`
            <div class="reading-progress">
                <div class="reading-progress-bar"></div>
            </div>
        `);

        $('body').append($progress);

        $(window).on('scroll', function() {
            const scrollTop = $(window).scrollTop();
            const docHeight = $(document).height() - $(window).height();
            const progress = (scrollTop / docHeight) * 100;

            $('.reading-progress-bar').css('width', Math.min(progress, 100) + '%');
        });
    }

    /**
     * Initialize smart defaults for better UX
     */
    function initSmartDefaults() {
        // Auto-collapse advanced sections on mobile
        if ($(window).width() <= 768) {
            $('.redco-card.collapsible').each(function() {
                const $card = $(this);
                const title = $card.find('h3').text().toLowerCase();

                if (title.includes('advanced') || title.includes('expert') || title.includes('configuration')) {
                    $card.addClass('collapsed');
                }
            });
        }

        // Auto-enable compact mode for very long pages
        const pageHeight = $(document).height();
        if (pageHeight > 4000) {
            $('.redco-module-content').addClass('compact-mode');
            $('.view-mode-btn[data-mode="compact"]').addClass('active');
            $('.view-mode-btn[data-mode="normal"]').removeClass('active');
        }
    }

    /**
     * Apply recommended tweaks for WordPress Core Tweaks module
     */
    function applyRecommendedTweaks($button) {
        const originalText = $button.text();
        $button.prop('disabled', true).text('Applying...');

        // Apply recommended settings
        const recommendedSettings = {
            'disable_emojis': true,
            'remove_version_numbers': true,
            'disable_xmlrpc': true,
            'limit_post_revisions': true,
            'disable_pingbacks': true,
            'remove_shortlink': true
        };

        $.ajax({
            url: redcoAjax.ajaxurl,
            type: 'POST',
            data: {
                action: 'redco_apply_recommended_tweaks',
                settings: recommendedSettings,
                nonce: redcoAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    showToast('Recommended tweaks applied successfully!', 'success');

                    // Update status badges
                    updateHeaderStatusBadges('wordpress-core-tweaks', recommendedSettings);

                    // Refresh page after delay
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    showToast(response.data.message || 'Failed to apply tweaks', 'error');
                }
            },
            error: function() {
                showToast('Network error occurred', 'error');
            },
            complete: function() {
                $button.prop('disabled', false).text(originalText);
            }
        });
    }

    /**
     * Reset all tweaks for WordPress Core Tweaks module
     */
    function resetAllTweaks($button) {
        if (!confirm('Are you sure you want to reset all WordPress Core Tweaks to default settings?')) {
            return;
        }

        const originalText = $button.text();
        $button.prop('disabled', true).text('Resetting...');

        $.ajax({
            url: redcoAjax.ajaxurl,
            type: 'POST',
            data: {
                action: 'redco_reset_all_tweaks',
                nonce: redcoAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    showToast('All tweaks reset to default settings', 'success');

                    // Refresh page after delay
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    showToast(response.data.message || 'Failed to reset tweaks', 'error');
                }
            },
            error: function() {
                showToast('Network error occurred', 'error');
            },
            complete: function() {
                $button.prop('disabled', false).text(originalText);
            }
        });
    }

    /**
     * Update header status badges
     */
    function updateHeaderStatusBadges(module, settings) {
        const $statusContainer = $('.header-status');

        // Update existing badges or add new ones based on settings
        Object.keys(settings).forEach(setting => {
            if (settings[setting]) {
                // Add or update status badge
                const badgeClass = getBadgeClassForSetting(setting);
                const badgeText = getBadgeTextForSetting(setting);
                const badgeIcon = getBadgeIconForSetting(setting);

                if (!$statusContainer.find(`.status-badge.${badgeClass}`).length) {
                    $statusContainer.append(`
                        <div class="status-badge ${badgeClass}">
                            <span class="dashicons ${badgeIcon}"></span>
                            ${badgeText}
                        </div>
                    `);
                }
            }
        });
    }

    /**
     * Get badge class for setting
     */
    function getBadgeClassForSetting(setting) {
        const badgeClasses = {
            'disable_emojis': 'performance',
            'remove_version_numbers': 'performance',
            'disable_xmlrpc': 'performance',
            'limit_post_revisions': 'performance',
            'critical_css': 'performance',
            'resource_hints': 'performance'
        };

        return badgeClasses[setting] || 'performance';
    }

    /**
     * Get badge text for setting
     */
    function getBadgeTextForSetting(setting) {
        const badgeTexts = {
            'disable_emojis': 'No Emojis',
            'remove_version_numbers': 'Security',
            'disable_xmlrpc': 'No XML-RPC',
            'limit_post_revisions': 'Limited Revisions',
            'critical_css': 'Critical CSS',
            'resource_hints': 'Resource Hints'
        };

        return badgeTexts[setting] || 'Active';
    }

    /**
     * Get badge icon for setting
     */
    function getBadgeIconForSetting(setting) {
        const badgeIcons = {
            'disable_emojis': 'dashicons-smiley',
            'remove_version_numbers': 'dashicons-shield',
            'disable_xmlrpc': 'dashicons-shield-alt',
            'limit_post_revisions': 'dashicons-backup',
            'critical_css': 'dashicons-chart-line',
            'resource_hints': 'dashicons-networking'
        };

        return badgeIcons[setting] || 'dashicons-yes-alt';
    }

})(jQuery);
