<?php
/**
 * WebP Module Debug Test
 *
 * Place this file in the plugin root directory and access it via:
 * /wp-content/plugins/redco-optimizer/webp-debug-test.php
 */

// Load WordPress
require_once('../../../wp-load.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

echo '<h1>WebP Module Debug Test</h1>';

// Check if the helper function exists
echo '<h2>1. Helper Function Check</h2>';
if (function_exists('redco_is_module_enabled')) {
    echo '✅ redco_is_module_enabled function exists<br>';

    $is_enabled = redco_is_module_enabled('smart-webp-conversion');
    echo 'Module enabled status: ' . ($is_enabled ? '✅ ENABLED' : '❌ DISABLED') . '<br>';
} else {
    echo '❌ redco_is_module_enabled function does NOT exist<br>';
}

// Check database option directly
echo '<h2>2. Database Option Check</h2>';
$plugin_options = get_option('redco_optimizer_options', array());
$enabled_modules = isset($plugin_options['modules_enabled']) ? $plugin_options['modules_enabled'] : array();
echo 'Plugin options: <pre>' . print_r($plugin_options, true) . '</pre>';
echo 'Enabled modules in database: <pre>' . print_r($enabled_modules, true) . '</pre>';

$is_webp_in_array = in_array('smart-webp-conversion', $enabled_modules);
echo 'WebP in enabled modules array: ' . ($is_webp_in_array ? '✅ YES' : '❌ NO') . '<br>';

// Check if class exists
echo '<h2>3. Class Check</h2>';
if (class_exists('Redco_Smart_WebP_Conversion')) {
    echo '✅ Redco_Smart_WebP_Conversion class exists<br>';
} else {
    echo '❌ Redco_Smart_WebP_Conversion class does NOT exist<br>';
}

// Check if WebP support is available
echo '<h2>4. Server WebP Support</h2>';
$webp_support = function_exists('imagewebp') && (imagetypes() & IMG_WEBP);
echo 'Server WebP support: ' . ($webp_support ? '✅ SUPPORTED' : '❌ NOT SUPPORTED') . '<br>';

if (function_exists('gd_info')) {
    $gd_info = gd_info();
    echo 'GD Version: ' . $gd_info['GD Version'] . '<br>';
    echo 'WebP Support: ' . (isset($gd_info['WebP Support']) && $gd_info['WebP Support'] ? '✅ YES' : '❌ NO') . '<br>';
}

// Check current page hook
echo '<h2>5. Current Page Info</h2>';
global $hook_suffix;
echo 'Current hook: ' . $hook_suffix . '<br>';
echo 'Is admin: ' . (is_admin() ? '✅ YES' : '❌ NO') . '<br>';

// Test manual module enabling
echo '<h2>6. Manual Module Test</h2>';
echo '<form method="post">';
echo '<button type="submit" name="enable_webp">Enable WebP Module</button>';
echo '<button type="submit" name="disable_webp">Disable WebP Module</button>';
echo '</form>';

if (isset($_POST['enable_webp'])) {
    $plugin_options = get_option('redco_optimizer_options', array());
    if (!isset($plugin_options['modules_enabled'])) {
        $plugin_options['modules_enabled'] = array();
    }

    if (!in_array('smart-webp-conversion', $plugin_options['modules_enabled'])) {
        $plugin_options['modules_enabled'][] = 'smart-webp-conversion';
        update_option('redco_optimizer_options', $plugin_options);
        echo '<div style="background:green;color:white;padding:10px;">✅ WebP module enabled!</div>';
    } else {
        echo '<div style="background:orange;color:white;padding:10px;">⚠️ WebP module was already enabled</div>';
    }
}

if (isset($_POST['disable_webp'])) {
    $plugin_options = get_option('redco_optimizer_options', array());
    if (isset($plugin_options['modules_enabled'])) {
        $key = array_search('smart-webp-conversion', $plugin_options['modules_enabled']);
        if ($key !== false) {
            unset($plugin_options['modules_enabled'][$key]);
            $plugin_options['modules_enabled'] = array_values($plugin_options['modules_enabled']);
            update_option('redco_optimizer_options', $plugin_options);
            echo '<div style="background:red;color:white;padding:10px;">❌ WebP module disabled!</div>';
        } else {
            echo '<div style="background:orange;color:white;padding:10px;">⚠️ WebP module was already disabled</div>';
        }
    } else {
        echo '<div style="background:orange;color:white;padding:10px;">⚠️ WebP module was already disabled</div>';
    }
}

echo '<br><a href="' . admin_url('admin.php?page=redco-optimizer-smart-webp-conversion') . '">Go to WebP Module Page</a>';
