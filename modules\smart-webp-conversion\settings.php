<?php
/**
 * Smart WebP Conversion Module Settings Page
 * 
 * @package Redco_Optimizer
 * @subpackage Smart_WebP_Conversion
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get module instance
$webp_module = new Redco_Smart_WebP_Conversion();
$stats = $webp_module->get_stats();
$settings = $webp_module->get_settings();

// Handle settings save
if (isset($_POST['save_webp_settings']) && wp_verify_nonce($_POST['webp_settings_nonce'], 'save_webp_settings')) {
    $new_settings = array(
        'auto_convert_uploads' => isset($_POST['auto_convert_uploads']),
        'replace_in_content' => isset($_POST['replace_in_content']),
        'quality' => max(1, min(100, intval($_POST['quality']))),
        'lossless' => isset($_POST['lossless']),
        'backup_originals' => isset($_POST['backup_originals']),
        'batch_size' => max(1, min(50, intval($_POST['batch_size']))),
        'max_execution_time' => max(10, min(300, intval($_POST['max_execution_time'])))
    );
    
    update_option('redco_webp_settings', $new_settings);
    $settings = $new_settings;
    
    echo '<div class="notice notice-success"><p>' . __('Settings saved successfully!', 'redco-optimizer') . '</p></div>';
}
?>

<div class="redco-module-container">
    <!-- Module Header -->
    <div class="module-header-section">
        <div class="header-breadcrumb">
            <span class="breadcrumb-item"><?php _e('Redco Optimizer', 'redco-optimizer'); ?></span>
            <span class="breadcrumb-separator">›</span>
            <span class="breadcrumb-item"><?php _e('Modules', 'redco-optimizer'); ?></span>
            <span class="breadcrumb-separator">›</span>
            <span class="breadcrumb-item breadcrumb-current"><?php _e('Smart WebP Conversion', 'redco-optimizer'); ?></span>
        </div>
        
        <div class="header-content">
            <div class="header-main">
                <div class="header-title-section">
                    <h1 class="header-title">
                        <span class="dashicons dashicons-format-image"></span>
                        <?php _e('Smart WebP Conversion', 'redco-optimizer'); ?>
                    </h1>
                    <p class="header-description">
                        <?php _e('Automatically convert images to WebP format for better performance while maintaining compatibility with all browsers.', 'redco-optimizer'); ?>
                    </p>
                </div>
                
                <div class="header-actions">
                    <button type="button" class="button button-secondary" id="test-webp-support">
                        <span class="dashicons dashicons-admin-tools"></span>
                        <?php _e('Test Server Support', 'redco-optimizer'); ?>
                    </button>
                    
                    <button type="button" class="button button-primary" id="bulk-convert-images" <?php echo !$stats['server_support'] ? 'disabled' : ''; ?>>
                        <span class="dashicons dashicons-update"></span>
                        <?php _e('Convert All Images', 'redco-optimizer'); ?>
                    </button>
                </div>
            </div>
            
            <div class="header-metrics">
                <div class="metric-card">
                    <div class="metric-value"><?php echo number_format($stats['converted_images']); ?></div>
                    <div class="header-metric-label"><?php _e('Converted Images', 'redco-optimizer'); ?></div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-value"><?php echo $stats['conversion_percentage']; ?>%</div>
                    <div class="header-metric-label"><?php _e('Conversion Rate', 'redco-optimizer'); ?></div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-value"><?php echo size_format($stats['total_savings']); ?></div>
                    <div class="header-metric-label"><?php _e('Space Saved', 'redco-optimizer'); ?></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Area -->
    <div class="redco-main-content">
        <div class="redco-content-wrapper">
            <div class="redco-content-area">
                
                <!-- Server Compatibility Check -->
                <?php if (!$stats['server_support']): ?>
                <div class="redco-alert redco-alert-error">
                    <div class="alert-icon">
                        <span class="dashicons dashicons-warning"></span>
                    </div>
                    <div class="alert-content">
                        <h3><?php _e('WebP Not Supported', 'redco-optimizer'); ?></h3>
                        <p><?php _e('Your server does not support WebP image conversion. Please contact your hosting provider to enable GD library with WebP support.', 'redco-optimizer'); ?></p>
                    </div>
                </div>
                <?php endif; ?>
                
                <!-- Settings Form -->
                <div class="module-settings-content">
                    <form method="post" action="">
                        <?php wp_nonce_field('save_webp_settings', 'webp_settings_nonce'); ?>
                        
                        <div class="settings-section">
                            <h2><?php _e('Conversion Settings', 'redco-optimizer'); ?></h2>
                            
                            <div class="setting-row">
                                <div class="setting-label">
                                    <label for="auto_convert_uploads">
                                        <?php _e('Auto-Convert New Uploads', 'redco-optimizer'); ?>
                                    </label>
                                    <p class="setting-description">
                                        <?php _e('Automatically convert new image uploads to WebP format.', 'redco-optimizer'); ?>
                                    </p>
                                </div>
                                <div class="setting-control">
                                    <label class="toggle-switch">
                                        <input type="checkbox" name="auto_convert_uploads" id="auto_convert_uploads" <?php checked($settings['auto_convert_uploads']); ?>>
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                            </div>
                            
                            <div class="setting-row">
                                <div class="setting-label">
                                    <label for="replace_in_content">
                                        <?php _e('Replace Images in Content', 'redco-optimizer'); ?>
                                    </label>
                                    <p class="setting-description">
                                        <?php _e('Automatically serve WebP images in post content for supported browsers.', 'redco-optimizer'); ?>
                                    </p>
                                </div>
                                <div class="setting-control">
                                    <label class="toggle-switch">
                                        <input type="checkbox" name="replace_in_content" id="replace_in_content" <?php checked($settings['replace_in_content']); ?>>
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                            </div>
                            
                            <div class="setting-row">
                                <div class="setting-label">
                                    <label for="backup_originals">
                                        <?php _e('Backup Original Images', 'redco-optimizer'); ?>
                                    </label>
                                    <p class="setting-description">
                                        <?php _e('Keep original images as backup (recommended for safety).', 'redco-optimizer'); ?>
                                    </p>
                                </div>
                                <div class="setting-control">
                                    <label class="toggle-switch">
                                        <input type="checkbox" name="backup_originals" id="backup_originals" <?php checked($settings['backup_originals']); ?>>
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="settings-section">
                            <h2><?php _e('Quality Settings', 'redco-optimizer'); ?></h2>
                            
                            <div class="setting-row">
                                <div class="setting-label">
                                    <label for="lossless">
                                        <?php _e('Lossless Compression', 'redco-optimizer'); ?>
                                    </label>
                                    <p class="setting-description">
                                        <?php _e('Use lossless compression for maximum quality (larger file sizes).', 'redco-optimizer'); ?>
                                    </p>
                                </div>
                                <div class="setting-control">
                                    <label class="toggle-switch">
                                        <input type="checkbox" name="lossless" id="lossless" <?php checked($settings['lossless']); ?>>
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                            </div>
                            
                            <div class="setting-row" id="quality-setting" style="<?php echo $settings['lossless'] ? 'display: none;' : ''; ?>">
                                <div class="setting-label">
                                    <label for="quality">
                                        <?php _e('Compression Quality', 'redco-optimizer'); ?>
                                    </label>
                                    <p class="setting-description">
                                        <?php _e('Quality level for lossy compression (1-100). Higher values mean better quality but larger files.', 'redco-optimizer'); ?>
                                    </p>
                                </div>
                                <div class="setting-control">
                                    <div class="quality-slider-container">
                                        <input type="range" name="quality" id="quality" min="1" max="100" value="<?php echo $settings['quality']; ?>" class="quality-slider">
                                        <span class="quality-value"><?php echo $settings['quality']; ?>%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="settings-section">
                            <h2><?php _e('Performance Settings', 'redco-optimizer'); ?></h2>
                            
                            <div class="setting-row">
                                <div class="setting-label">
                                    <label for="batch_size">
                                        <?php _e('Batch Size', 'redco-optimizer'); ?>
                                    </label>
                                    <p class="setting-description">
                                        <?php _e('Number of images to process in each batch during bulk conversion.', 'redco-optimizer'); ?>
                                    </p>
                                </div>
                                <div class="setting-control">
                                    <input type="number" name="batch_size" id="batch_size" value="<?php echo $settings['batch_size']; ?>" min="1" max="50" class="small-text">
                                </div>
                            </div>
                            
                            <div class="setting-row">
                                <div class="setting-label">
                                    <label for="max_execution_time">
                                        <?php _e('Max Execution Time', 'redco-optimizer'); ?>
                                    </label>
                                    <p class="setting-description">
                                        <?php _e('Maximum time (in seconds) to spend on each batch conversion.', 'redco-optimizer'); ?>
                                    </p>
                                </div>
                                <div class="setting-control">
                                    <input type="number" name="max_execution_time" id="max_execution_time" value="<?php echo $settings['max_execution_time']; ?>" min="10" max="300" class="small-text">
                                </div>
                            </div>
                        </div>
                        
                        <div class="settings-actions">
                            <button type="submit" name="save_webp_settings" class="button button-primary">
                                <span class="dashicons dashicons-yes"></span>
                                <?php _e('Save Settings', 'redco-optimizer'); ?>
                            </button>
                        </div>
                    </form>
                </div>
                
                <!-- Conversion History -->
                <div class="module-settings-content">
                    <h2><?php _e('Recent Conversions', 'redco-optimizer'); ?></h2>
                    
                    <?php if (empty($stats['recent_conversions'])): ?>
                        <div class="redco-empty-state">
                            <div class="empty-state-icon">
                                <span class="dashicons dashicons-format-image"></span>
                            </div>
                            <h3><?php _e('No Conversions Yet', 'redco-optimizer'); ?></h3>
                            <p><?php _e('Start converting your images to WebP format to see conversion history here.', 'redco-optimizer'); ?></p>
                        </div>
                    <?php else: ?>
                        <div class="conversion-history-table">
                            <table class="wp-list-table widefat fixed striped">
                                <thead>
                                    <tr>
                                        <th><?php _e('Date', 'redco-optimizer'); ?></th>
                                        <th><?php _e('Type', 'redco-optimizer'); ?></th>
                                        <th><?php _e('Original Size', 'redco-optimizer'); ?></th>
                                        <th><?php _e('WebP Size', 'redco-optimizer'); ?></th>
                                        <th><?php _e('Savings', 'redco-optimizer'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($stats['recent_conversions'] as $conversion): ?>
                                        <tr>
                                            <td><?php echo date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($conversion['timestamp'])); ?></td>
                                            <td>
                                                <span class="conversion-type conversion-type-<?php echo esc_attr($conversion['type']); ?>">
                                                    <?php echo ucfirst($conversion['type']); ?>
                                                </span>
                                            </td>
                                            <td><?php echo size_format($conversion['original_size']); ?></td>
                                            <td><?php echo size_format($conversion['webp_size']); ?></td>
                                            <td class="savings-positive">
                                                <?php echo size_format($conversion['savings']); ?>
                                                (<?php echo round(($conversion['savings'] / $conversion['original_size']) * 100, 1); ?>%)
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Sidebar -->
            <div class="redco-sidebar">
                <!-- Statistics Card -->
                <div class="sidebar-card">
                    <div class="card-header">
                        <h3><?php _e('Conversion Statistics', 'redco-optimizer'); ?></h3>
                    </div>
                    <div class="card-content">
                        <div class="stat-item">
                            <div class="stat-label"><?php _e('Total Images', 'redco-optimizer'); ?></div>
                            <div class="stat-value"><?php echo number_format($stats['total_images']); ?></div>
                        </div>
                        
                        <div class="stat-item">
                            <div class="stat-label"><?php _e('Converted', 'redco-optimizer'); ?></div>
                            <div class="stat-value"><?php echo number_format($stats['converted_images']); ?></div>
                        </div>
                        
                        <div class="stat-item">
                            <div class="stat-label"><?php _e('Remaining', 'redco-optimizer'); ?></div>
                            <div class="stat-value"><?php echo number_format($stats['unconverted_images']); ?></div>
                        </div>
                        
                        <div class="stat-item">
                            <div class="stat-label"><?php _e('Total Savings', 'redco-optimizer'); ?></div>
                            <div class="stat-value"><?php echo size_format($stats['total_savings']); ?></div>
                        </div>
                        
                        <div class="stat-item">
                            <div class="stat-label"><?php _e('Average Savings', 'redco-optimizer'); ?></div>
                            <div class="stat-value"><?php echo $stats['savings_percentage']; ?>%</div>
                        </div>
                    </div>
                </div>
                
                <!-- Server Support Card -->
                <div class="sidebar-card">
                    <div class="card-header">
                        <h3><?php _e('Server Compatibility', 'redco-optimizer'); ?></h3>
                    </div>
                    <div class="card-content">
                        <div class="compatibility-item">
                            <span class="compatibility-label"><?php _e('WebP Support', 'redco-optimizer'); ?></span>
                            <span class="compatibility-status <?php echo $stats['server_support'] ? 'status-supported' : 'status-unsupported'; ?>">
                                <?php echo $stats['server_support'] ? __('Supported', 'redco-optimizer') : __('Not Supported', 'redco-optimizer'); ?>
                            </span>
                        </div>
                        
                        <div class="compatibility-item">
                            <span class="compatibility-label"><?php _e('Browser Support', 'redco-optimizer'); ?></span>
                            <span class="compatibility-status <?php echo $stats['browser_support'] ? 'status-supported' : 'status-unsupported'; ?>">
                                <?php echo $stats['browser_support'] ? __('Supported', 'redco-optimizer') : __('Not Supported', 'redco-optimizer'); ?>
                            </span>
                        </div>
                    </div>
                </div>
                
                <!-- Help Card -->
                <div class="sidebar-card">
                    <div class="card-header">
                        <h3><?php _e('Need Help?', 'redco-optimizer'); ?></h3>
                    </div>
                    <div class="card-content">
                        <p><?php _e('WebP is a modern image format that provides superior compression compared to JPEG and PNG.', 'redco-optimizer'); ?></p>
                        
                        <div class="help-links">
                            <a href="#" class="help-link">
                                <span class="dashicons dashicons-book"></span>
                                <?php _e('Documentation', 'redco-optimizer'); ?>
                            </a>
                            
                            <a href="#" class="help-link">
                                <span class="dashicons dashicons-sos"></span>
                                <?php _e('Support', 'redco-optimizer'); ?>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Progress Modal -->
<div id="webp-progress-modal" class="redco-modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h2><?php _e('Converting Images to WebP', 'redco-optimizer'); ?></h2>
        </div>
        <div class="modal-body">
            <div class="progress-container">
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 0%;"></div>
                </div>
                <div class="progress-text">
                    <span class="progress-current">0</span> / <span class="progress-total">0</span> images converted
                </div>
            </div>
            
            <div class="conversion-details">
                <div class="current-file"></div>
                <div class="conversion-stats">
                    <div class="stat">
                        <span class="label"><?php _e('Processed:', 'redco-optimizer'); ?></span>
                        <span class="value processed-count">0</span>
                    </div>
                    <div class="stat">
                        <span class="label"><?php _e('Errors:', 'redco-optimizer'); ?></span>
                        <span class="value error-count">0</span>
                    </div>
                    <div class="stat">
                        <span class="label"><?php _e('Savings:', 'redco-optimizer'); ?></span>
                        <span class="value total-savings">0 KB</span>
                    </div>
                </div>
            </div>
            
            <div class="conversion-log">
                <h4><?php _e('Conversion Log', 'redco-optimizer'); ?></h4>
                <div class="log-entries"></div>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="button button-secondary" id="cancel-conversion" style="display: none;">
                <?php _e('Cancel', 'redco-optimizer'); ?>
            </button>
            <button type="button" class="button button-primary" id="close-conversion-modal" style="display: none;">
                <?php _e('Close', 'redco-optimizer'); ?>
            </button>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Quality slider interaction
    $('#lossless').change(function() {
        if ($(this).is(':checked')) {
            $('#quality-setting').hide();
        } else {
            $('#quality-setting').show();
        }
    });
    
    $('#quality').on('input', function() {
        $('.quality-value').text($(this).val() + '%');
    });
});
</script>
